@softwareversion {delebecque:hal-02090402-condensed,
  title = {Scila<PERSON>},
  author = {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>{\c c}ois and <PERSON>, <PERSON> and <PERSON>, <PERSON> 
    and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON>},
  url = {https://www.scilab.org/},
  date = {1994-01},
  file = {https://hal.inria.fr/hal-02090402/file/scilab-1.1.tar.gz},
  institution = {Inria},
  license = {Scilab license},
  hal_id = {hal-02090402},
  hal_version = {v1},
  swhid = {swh:1:dir:1ba0b67b5d0c8f10961d878d91ae9d6e499d746a;
	   origin=https://hal.archives-ouvertes.fr/hal-02090402},
  version = {1.1},
  note = {First Scilab version. It was distributed by anonymous ftp.},
  repository= {https://github.com/scilab/scilab},
  abstract = {Software for Numerical Computation freely distributed.}
}
@software {deleb<PERSON>que:hal-02090402,
  title = {Scila<PERSON>},
  author = {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>{\c c}ois and <PERSON>, <PERSON> and Goursat, <PERSON> 
    and <PERSON>oukhah, <PERSON><PERSON> and <PERSON>eer, <PERSON> and <PERSON>lier, <PERSON>-<PERSON>},
  date = {1994},
  institution = {Inria},
  license = {Scilab license},
  hal_id = {hal-02090402},
  hal_version = {v1},
  url = {https://www.scilab.org/},
  abstract = {Software for Numerical Computation freely distributed.},
  repository= {https://github.com/scilab/scilab},
}

@softwareversion {delebecque:hal-02090402v1,
  version = {1.1},
  date = {1994-01},
  file = {https://hal.inria.fr/hal-02090402/file/scilab-1.1.tar.gz},
  swhid = {swh:1:dir:1ba0b67b5d0c8f10961d878d91ae9d6e499d746a;
	   origin=https://hal.archives-ouvertes.fr/hal-02090402},
  note = {First Scilab version. It was distributed by anonymous ftp.},
  crossref = {delebecque:hal-02090402}
}
 @software {cgal,
  title = {The Computational Geometry Algorithms Library},
  author = {{The CGAL Project}},
  editor = {{CGAL Editorial Board}},
  date = {1996},
  url = {https://cgal.org/}
 }

 @softwareversion{cgal:5-0-2,
  crossref = {cgal},
  version = {{5.0.2}},
  url = {https://docs.cgal.org/5.02},
  date = {2020},
  swhid = {swh:1:rel:636541bbf6c77863908eae744610a3d91fa58855;
           origin=https://github.com/CGAL/cgal/}
 }

 @softwaremodule{cgal:lp-gi-20a,
  crossref = {cgal:5-0-2},
  author = {Menelaos Karavelas},
  subtitle = {{2D} Voronoi Diagram Adaptor},
  license = {GPL},
  introducedin = {cgal:3-1},
  url = {https://doc.cgal.org/5.0.2/Manual/packages.html#PkgVoronoiDiagram2},
 }
 @softwaremodule{cgal:lp-gi-20a-condensed,
  title = {The Computational Geometry Algorithms Library},
  subtitle = {{2D} Voronoi Diagram Adaptor},
  author = {Menelaos Karavelas},
  editor = {{CGAL Editorial Board}},
  license = {GPL},
  version = {{5.0.2}},
  introducedin = {cgal:3-1},
  date = {2020},
  swhid = {swh:1:rel:636541bbf6c77863908eae744610a3d91fa58855;
           origin=https://github.com/CGAL/cgal/},
  url = {https://doc.cgal.org/5.0.2/Manual/packages.html#PkgVoronoiDiagram2},
 }
@software {parmap,
  title = {The Parmap library},
  author = {Di Cosmo, Roberto and Marco Danelutto},
  date = {2012},
  institution = {{Inria} and {University of Paris} and {University of Pisa}},
  license = {LGPL-2.0},
  url = {https://rdicosmo.github.io/parmap/},
  repository= {https://github.com/rdicosmo/parmap},
}

@softwareversion {parmap-1.1.1,
  crossref = {parmap},
  date = {2020},
  version = {1.1.1},
  swhid = {swh:1:rel:373e2604d96de4ab1d505190b654c5c4045db773;
     origin=https://github.com/rdicosmo/parmap;
     visit=swh:1:snp:2a6c348c53eb77d458f24c9cbcecaf92e3c45615},
}

@codefragment {simplemapper,
  subtitle = {Core mapping routine},
  swhid = {swh:1:cnt:43a6b232768017b03da934ba22d9cc3f2726a6c5;
     origin=https://github.com/rdicosmo/parmap;
     visit=swh:1:snp:2a6c348c53eb77d458f24c9cbcecaf92e3c45615;
     anchor=swh:1:rel:373e2604d96de4ab1d505190b654c5c4045db773;
     path=/src/parmap.ml;
     lines=192-228},
  crossref = {parmap-1.1.1}
}
@codefragment {simplemapper-condensed,
  title = {The Parmap library},
  author = {Di Cosmo, Roberto and Marco Danelutto},
  date = {2020},
  institution = {{Inria} and {University of Paris} and {University of Pisa}},
  license = {LGPL-2.0},
  url = {https://rdicosmo.github.io/parmap/},
  repository= {https://github.com/rdicosmo/parmap},
  version = {1.1.1},
  subtitle = {Core mapping routine},
  swhid = {swh:1:cnt:43a6b232768017b03da934ba22d9cc3f2726a6c5;
     origin=https://github.com/rdicosmo/parmap;
     visit=swh:1:snp:2a6c348c53eb77d458f24c9cbcecaf92e3c45615;
     anchor=swh:1:rel:373e2604d96de4ab1d505190b654c5c4045db773;
     path=/src/parmap.ml;
     lines=192-228}
}

@article{ad-wood-2003,
  author    = {Christopher Anderson and Sophia Drossopoulou},
  title     = {{BabyJ}: from Object Based to Class Based Programming via Types},
  journal   = {{WOOD}},
  volume    = {82},
  number    = {7},
  pages     = {53--81},
  year      = {2003}
}

@softwareversion{gf-tag-sound-repo,
  title={tag-sound},
  author={Ben Greenman and Matthias Felleisen},
  swhid={swh:1:dir:cd0b0abeee707e57cd699e2e2ebd075da8ebf1f7;origin=https://github.com/nuprl/tag-sound;visit=swh:1:snp:7967bc0abee8bf3bfffb9252207a07b73538525a;anchor=swh:1:rev:4cc09ca228947a99c8f4ac45eefb76e96ee96e53},
  repository={https://github.com/nuprl/tag-sound},
  version={4cc09ca},
  date={2020}
}