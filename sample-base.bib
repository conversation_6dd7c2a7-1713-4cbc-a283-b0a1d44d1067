
% Journals

% First the Full Name is given, then the abbreviation used in the AMS Math
% Reviews, with an indication if it could not be found there.
% Note the 2nd overwrites the 1st, so swap them if you want the full name.

 %{AMS}
 @String{AMSTrans = "American Mathematical Society Translations" }
 @String{AMSTrans = "Amer. Math. Soc. Transl." }
 @String{BullAMS = "Bulletin of the American Mathematical Society" }
 @String{BullAMS = "Bull. Amer. Math. Soc." }
 @String{ProcAMS = "Proceedings of the American Mathematical Society" }
 @String{ProcAMS = "Proc. Amer. Math. Soc." }
 @String{TransAMS = "Transactions of the American Mathematical Society" }
 @String{TransAMS = "Trans. Amer. Math. Soc." }

 %ACM
 @String{CACM = "Communications of the {ACM}" }
 @String{CACM = "Commun. {ACM}" }
 @String{CompServ = "Comput. Surveys" }
 @String{JACM = "J. ACM" }
 @String{ACMMathSoft = "{ACM} Transactions on Mathematical Software" }
 @String{ACMMathSoft = "{ACM} Trans. Math. Software" }
 @String{SIGNUM = "{ACM} {SIGNUM} Newsletter" }
 @String{SIGNUM = "{ACM} {SIGNUM} Newslett." }

 @String{AmerSocio = "American Journal of Sociology" }
 @String{AmerStatAssoc = "Journal of the American Statistical Association" }
 @String{AmerStatAssoc = "J. Amer. Statist. Assoc." }
 @String{ApplMathComp = "Applied Mathematics and Computation" }
 @String{ApplMathComp = "Appl. Math. Comput." }
 @String{AmerMathMonthly = "American Mathematical Monthly" }
 @String{AmerMathMonthly = "Amer. Math. Monthly" }
 @String{BIT = "{BIT}" }
 @String{BritStatPsych = "British Journal of Mathematical and Statistical
          Psychology" }
 @String{BritStatPsych = "Brit. J. Math. Statist. Psych." }
 @String{CanMathBull = "Canadian Mathematical Bulletin" }
 @String{CanMathBull = "Canad. Math. Bull." }
 @String{CompApplMath = "Journal of Computational and Applied Mathematics" }
 @String{CompApplMath = "J. Comput. Appl. Math." }
 @String{CompPhys = "Journal of Computational Physics" }
 @String{CompPhys = "J. Comput. Phys." }
 @String{CompStruct = "Computers and Structures" }
 @String{CompStruct = "Comput. \& Structures" }
 @String{CompJour = "The Computer Journal" }
 @String{CompJour = "Comput. J." }
 @String{CompSysSci = "Journal of Computer and System Sciences" }
 @String{CompSysSci = "J. Comput. System Sci." }
 @String{Computing = "Computing" }
 @String{ContempMath = "Contemporary Mathematics" }
 @String{ContempMath = "Contemp. Math." }
 @String{Crelle = "Crelle's Journal" }
 @String{GiornaleMath = "Giornale di Mathematiche" }
 @String{GiornaleMath = "Giorn. Mat." } % didn't find in AMS MR., ibid.

 %IEEE
 @String{Computer = "{IEEE} Computer" }
 @String{IEEETransComp = "{IEEE} Transactions on Computers" }
 @String{IEEETransComp = "{IEEE} Trans. Comput." }
 @String{IEEETransAC = "{IEEE} Transactions on Automatic Control" }
 @String{IEEETransAC = "{IEEE} Trans. Automat. Control" }
 @String{IEEESpec = "{IEEE} Spectrum" } % didn't find in AMS MR
 @String{ProcIEEE = "Proceedings of the {IEEE}" }
 @String{ProcIEEE = "Proc. {IEEE}" } % didn't find in AMS MR
 @String{IEEETransAeroElec = "{IEEE} Transactions on Aerospace and Electronic
     Systems" }
 @String{IEEETransAeroElec = "{IEEE} Trans. Aerospace Electron. Systems" }

 @String{IMANumerAna = "{IMA} Journal of Numerical Analysis" }
 @String{IMANumerAna = "{IMA} J. Numer. Anal." }
 @String{InfProcLet = "Information Processing Letters" }
 @String{InfProcLet = "Inform. Process. Lett." }
 @String{InstMathApp = "Journal of the Institute of Mathematics and
     its Applications" }
 @String{InstMathApp = "J. Inst. Math. Appl." }
 @String{IntControl = "International Journal of Control" }
 @String{IntControl = "Internat. J. Control" }
 @String{IntNumerEng = "International Journal for Numerical Methods in
     Engineering" }
 @String{IntNumerEng = "Internat. J. Numer. Methods Engrg." }
 @String{IntSuper = "International Journal of Supercomputing Applications" }
 @String{IntSuper = "Internat. J. Supercomputing Applic." } % didn't find
%% in AMS MR
 @String{Kibernetika = "Kibernetika" }
 @String{JResNatBurStand = "Journal of Research of the National Bureau
     of Standards" }
 @String{JResNatBurStand = "J. Res. Nat. Bur. Standards" }
 @String{LinAlgApp = "Linear Algebra and its Applications" }
 @String{LinAlgApp = "Linear Algebra Appl." }
 @String{MathAnaAppl = "Journal of Mathematical Analysis and Applications" }
 @String{MathAnaAppl = "J. Math. Anal. Appl." }
 @String{MathAnnalen = "Mathematische Annalen" }
 @String{MathAnnalen = "Math. Ann." }
 @String{MathPhys = "Journal of Mathematical Physics" }
 @String{MathPhys = "J. Math. Phys." }
 @String{MathComp = "Mathematics of Computation" }
 @String{MathComp = "Math. Comp." }
 @String{MathScand = "Mathematica Scandinavica" }
 @String{MathScand = "Math. Scand." }
 @String{TablesAidsComp = "Mathematical Tables and Other Aids to Computation" }
 @String{TablesAidsComp = "Math. Tables Aids Comput." }
 @String{NumerMath = "Numerische Mathematik" }
 @String{NumerMath = "Numer. Math." }
 @String{PacificMath = "Pacific Journal of Mathematics" }
 @String{PacificMath = "Pacific J. Math." }
 @String{ParDistComp = "Journal of Parallel and Distributed Computing" }
 @String{ParDistComp = "J. Parallel and Distrib. Comput." } % didn't find
%% in AMS MR
 @String{ParComputing = "Parallel Computing" }
 @String{ParComputing = "Parallel Comput." }
 @String{PhilMag = "Philosophical Magazine" }
 @String{PhilMag = "Philos. Mag." }
 @String{ProcNAS = "Proceedings of the National Academy of Sciences
                    of the USA" }
 @String{ProcNAS = "Proc. Nat. Acad. Sci. U. S. A." }
 @String{Psychometrika = "Psychometrika" }
 @String{QuartMath = "Quarterly Journal of Mathematics, Oxford, Series (2)" }
 @String{QuartMath = "Quart. J. Math. Oxford Ser. (2)" }
 @String{QuartApplMath = "Quarterly of Applied Mathematics" }
 @String{QuartApplMath = "Quart. Appl. Math." }
 @String{RevueInstStat = "Review of the International Statisical Institute" }
 @String{RevueInstStat = "Rev. Inst. Internat. Statist." }

 %SIAM
 @String{JSIAM = "Journal of the Society for Industrial and Applied
     Mathematics" }
 @String{JSIAM = "J. Soc. Indust. Appl. Math." }
 @String{JSIAMB = "Journal of the Society for Industrial and Applied
     Mathematics, Series B, Numerical Analysis" }
 @String{JSIAMB = "J. Soc. Indust. Appl. Math. Ser. B Numer. Anal." }
 @String{SIAMAlgMeth = "{SIAM} Journal on Algebraic and Discrete Methods" }
 @String{SIAMAlgMeth = "{SIAM} J. Algebraic Discrete Methods" }
 @String{SIAMAppMath = "{SIAM} Journal on Applied Mathematics" }
 @String{SIAMAppMath = "{SIAM} J. Appl. Math." }
 @String{SIAMComp = "{SIAM} Journal on Computing" }
 @String{SIAMComp = "{SIAM} J. Comput." }
 @String{SIAMMatrix = "{SIAM} Journal on Matrix Analysis and Applications" }
 @String{SIAMMatrix = "{SIAM} J. Matrix Anal. Appl." }
 @String{SIAMNumAnal = "{SIAM} Journal on Numerical Analysis" }
 @String{SIAMNumAnal = "{SIAM} J. Numer. Anal." }
 @String{SIAMReview = "{SIAM} Review" }
 @String{SIAMReview = "{SIAM} Rev." }
 @String{SIAMSciStat = "{SIAM} Journal on Scientific and Statistical
     Computing" }
 @String{SIAMSciStat = "{SIAM} J. Sci. Statist. Comput." }

 @String{SoftPracExp = "Software Practice and Experience" }
 @String{SoftPracExp = "Software Prac. Experience" } % didn't find in AMS MR
 @String{StatScience = "Statistical Science" }
 @String{StatScience = "Statist. Sci." }
 @String{Techno = "Technometrics" }
 @String{USSRCompMathPhys = "{USSR} Computational Mathematics and Mathematical
     Physics" }
 @String{USSRCompMathPhys = "{U. S. S. R.} Comput. Math. and Math. Phys." }
 @String{VLSICompSys = "Journal of {VLSI} and Computer Systems" }
 @String{VLSICompSys = "J. {VLSI} Comput. Syst." }
 @String{ZAngewMathMech = "Zeitschrift fur Angewandte Mathematik und
     Mechanik" }
 @String{ZAngewMathMech = "Z. Angew. Math. Mech." }
 @String{ZAngewMathPhys = "Zeitschrift fur Angewandte Mathematik und Physik" }
 @String{ZAngewMathPhys = "Z. Angew. Math. Phys." }

% Publishers % ================================================= |

 @String{Academic = "Academic Press" }
 @String{ACMPress = "{ACM} Press" }
 @String{AdamHilger = "Adam Hilger" }
 @String{AddisonWesley = "Addison-Wesley" }
 @String{AllynBacon = "Allyn and Bacon" }
 @String{AMS = "American Mathematical Society" }
 @String{Birkhauser = "Birkha{\"u}ser" }
 @String{CambridgePress = "Cambridge University Press" }
 @String{Chelsea = "Chelsea" }
 @String{ClaredonPress = "Claredon Press" }
 @String{DoverPub = "Dover Publications" }
 @String{Eyolles = "Eyolles" }
 @String{HoltRinehartWinston = "Holt, Rinehart and Winston" }
 @String{Interscience = "Interscience" }
 @String{JohnsHopkinsPress = "The Johns Hopkins University Press" }
 @String{JohnWileySons = "John Wiley and Sons" }
 @String{Macmillan = "Macmillan" }
 @String{MathWorks = "The Math Works Inc." }
 @String{McGrawHill = "McGraw-Hill" }
 @String{NatBurStd = "National Bureau of Standards" }
 @String{NorthHolland = "North-Holland" }
 @String{OxfordPress = "Oxford University Press" }  %address Oxford or London?
 @String{PergamonPress = "Pergamon Press" }
 @String{PlenumPress = "Plenum Press" }
 @String{PrenticeHall = "Prentice-Hall" }
 @String{SIAMPub = "{SIAM} Publications" }
 @String{Springer = "Springer-Verlag" }
 @String{TexasPress = "University of Texas Press" }
 @String{VanNostrand = "Van Nostrand" }
 @String{WHFreeman = "W. H. Freeman and Co." }

%Entries

@inproceedings{verbitski2017amazon,
  title={Amazon aurora: Design considerations for high throughput cloud-native relational databases},
  author={Verbitski, Alexandre and Gupta, Anurag and Saha, Debanjan and Brahmadesam, Murali and Gupta, Kamal and Mittal, Raman and Krishnamurthy, Sailesh and Maurice, Sandor and Kharatishvili, Tengiz and Bao, Xiaofeng},
  booktitle={Proceedings of the 2017 ACM International Conference on Management of Data},
  pages={1041--1052},
  year={2017}
}

@inproceedings{dageville2016snowflake,
  title={The snowflake elastic data warehouse},
  author={Dageville, Benoit and Cruanes, Thierry and Zukowski, Marcin and Antonov, Vadim and Avanes, Artin and Bock, Jon and Claybaugh, Jonathan and Engovatov, Daniel and Hentschel, Martin and Huang, Jiansheng and others},
  booktitle={Proceedings of the 2016 International Conference on Management of Data},
  pages={215--226},
  year={2016}
}

@article{chappell2010introducing,
  title={Introducing the windows azure platform},
  author={Chappell, David and others},
  journal={David Chappell \& Associates White Paper},
  year={2010}
}

@techreport{dierks2008transport,
  title={The transport layer security (TLS) protocol version 1.2},
  author={Dierks, Tim and Rescorla, Eric},
  year={2008}
}

@article{deshmukh2011transparent,
  title={Transparent Data Encryption-Solution for Security of Database Contents},
  author={Deshmukh, Anwar Pasha Abdul Gafoor and Qureshi, Riyazuddin},
  journal={International Journal of Advanced Computer Science and Applications},
  volume={2},
  number={3},
  year={2011},
  publisher={Science and Information (SAI) Organization Limited}
}

@inproceedings{popa2011cryptdb,
  title={CryptDB: protecting confidentiality with encrypted query processing},
  author={Popa, Raluca Ada and Redfield, Catherine MS and Zeldovich, Nickolai and Balakrishnan, Hari},
  booktitle={Proceedings of the twenty-third ACM symposium on operating systems principles},
  pages={85--100},
  year={2011}
}

@inproceedings{antonopoulos2020azure,
  title={Azure SQL database always encrypted},
  author={Antonopoulos, Panagiotis and Arasu, Arvind and Singh, Kunal D and Eguro, Ken and Gupta, Nitish and Jain, Rajat and Kaushik, Raghav and Kodavalla, Hanuma and Kossmann, Donald and Ogg, Nikolas and others},
  booktitle={Proceedings of the 2020 ACM SIGMOD International Conference on Management of Data},
  pages={1511--1525},
  year={2020}
}


@online{mongodb,
  author =       "MongoDB",
  year =         "2022",
  title =        "Queryable Encryption: Protect your confidential workloads.",
  url =          "https://www.mongodb.com/products/queryable-encryption"
}

@inproceedings{sabt2015trusted,
  title={Trusted execution environment: what it is, and what it is not},
  author={Sabt, Mohamed and Achemlal, Mohammed and Bouabdallah, Abdelmadjid},
  booktitle={2015 IEEE Trustcom/BigDataSE/ISPA},
  volume={1},
  pages={57--64},
  year={2015},
  organization={IEEE}
}

@article{daemen1999aes,
  title={AES proposal: Rijndael},
  author={Daemen, Joan and Rijmen, Vincent},
  year={1999},
  publisher={Gaithersburg, MD, USA}
}

@article{coker2011principles,
  title={Principles of remote attestation},
  author={Coker, George and Guttman, Joshua and Loscocco, Peter and Herzog, Amy and Millen, Jonathan and O’Hanlon, Brian and Ramsdell, John and Segall, Ariel and Sheehy, Justin and Sniffen, Brian},
  journal={International Journal of Information Security},
  volume={10},
  number={2},
  pages={63--81},
  year={2011},
  publisher={Springer}
}

@inproceedings{gentry2009fully,
  title={Fully homomorphic encryption using ideal lattices},
  author={Gentry, Craig},
  booktitle={Proceedings of the forty-first annual ACM symposium on Theory of computing},
  pages={169--178},
  year={2009}
}

@inproceedings{cheon2017homomorphic,
  title={Homomorphic encryption for arithmetic of approximate numbers},
  author={Cheon, Jung Hee and Kim, Andrey and Kim, Miran and Song, Yongsoo},
  booktitle={International conference on the theory and application of cryptology and information security},
  pages={409--437},
  year={2017},
  organization={Springer}
}

@article{ogburn2013homomorphic,
  title={Homomorphic encryption},
  author={Ogburn, Monique and Turner, Claude and Dahal, Pushkar},
  journal={Procedia Computer Science},
  volume={20},
  pages={502--509},
  year={2013},
  publisher={Elsevier}
}

@article{poddar12arx,
  title={Arx: An Encrypted Database using Semantically Secure Encryption},
  author={Poddar, Rishabh and Boelter, Tobias and Popa, Raluca Ada},
  journal={Proceedings of the VLDB Endowment},
  volume={12},
  number={11},
  pages={1664--1678},
  year={2019}
}

@inproceedings{boldyreva2011order,
  title={Order-preserving encryption revisited: Improved security analysis and alternative solutions},
  author={Boldyreva, Alexandra and Chenette, Nathan and O’Neill, Adam},
  booktitle={Annual Cryptology Conference},
  pages={578--595},
  year={2011},
  organization={Springer}
}

@online{polardb,
  author =       "Alibaba Cloud",
  year =         "2021",
  title =        "End-to-end data encryption",
  url =          "https://www.alibabacloud.com/help/polardb-for-oracle/latest/end-to-end-data-encryption"
}

@online{azure,
  author =       "Microsoft SQL Sever 2022 Preview",
  year =         "2022",
  month=     "09",	
  title =        "Always Encrypted with secure enclaves",
  url =          "https://learn.microsoft.com/en-us/sql/relational-databases/security/encryption/always-encrypted-enclaves"
}

@inproceedings{van2021cacheout,
  title={CacheOut: Leaking data on Intel CPUs via cache evictions},
  author={van Schaik, Stephan and Minkin, Marina and Kwong, Andrew and Genkin, Daniel and Yarom, Yuval},
  booktitle={2021 IEEE Symposium on Security and Privacy (SP)},
  pages={339--354},
  year={2021},
  organization={IEEE}
}

@inproceedings{brasser2017software,
  title={Software grand exposure:$\{$SGX$\}$ cache attacks are practical},
  author={Brasser, Ferdinand and M{\"u}ller, Urs and Dmitrienko, Alexandra and Kostiainen, Kari and Capkun, Srdjan and Sadeghi, Ahmad-Reza},
  booktitle={11th USENIX Workshop on Offensive Technologies (WOOT 17)},
  year={2017}
}

@inproceedings{oleksenko2018varys,
  title={Varys: Protecting $\{$SGX$\}$ enclaves from practical side-channel attacks},
  author={Oleksenko, Oleksii and Trach, Bohdan and Krahn, Robert and Silberstein, Mark and Fetzer, Christof},
  booktitle={2018 $\{$Usenix$\}$ Annual Technical Conference ($\{$USENIX$\}$$\{$ATC$\}$ 18)},
  pages={227--240},
  year={2018}
}

@inproceedings{arasu2015transaction,
  title={Transaction processing on confidential data using cipherbase},
  author={Arasu, Arvind and Eguro, Ken and Joglekar, Manas and Kaushik, Raghav and Kossmann, Donald and Ramamurthy, Ravi},
  booktitle={2015 IEEE 31st International Conference on Data Engineering},
  pages={435--446},
  year={2015},
  organization={IEEE}
}

@inproceedings{bajaj2011trusteddb,
  title={TrustedDB: a trusted hardware based database with privacy and data confidentiality},
  author={Bajaj, Sumeet and Sion, Radu},
  booktitle={Proceedings of the 2011 ACM SIGMOD International Conference on Management of data},
  pages={205--216},
  year={2011}
}

@article{samuel2013processing,
  title={Processing Analytical Queries over Encrypted Data},
  author={Samuel, Stephen Tu M Frans Kaashoek and Zeldovich, Madden Nickolai},
  journal={Proceedings of the VLDB Endowment},
  volume={6},
  number={5},
  year={2013}
}

@online{tdxconnect,
  author = "Intel Trust Domain Extension",
  year = "2023",
  title = "Intel TDX Connect Architecture
Specification",
url = "https://www.intel.com/content/www/us/en/content-details/773614/intel-tdx-connect-architecture-specification.html"
}

@online{sevtio,
author = "AMD Secure Encrypted Virtualization",
year = "2023",
title = "AMD SEV-TIO: Trusted I/O for Secure
Encrypted Virtualization",
url = "https://www.amd.com/system/files/documents/sev-tio-whitepaper.pdf
"
}

@online{TDISP,
author="Nick Flaherty",
year = "2023",
title = "Security IP and controller for TDISP in PCI Express 6.0",
url = "https://www.eenewseurope.com/en/security-ip-and-controller-for-tdisp-in-pci-express-6-0/"
}

@online{sgxmem,
  author =       "Intel Software Guard Extentsions",
  year =         "2017",
  title =        "Enclave Memory Measurement Tool for Intel Software Guard Extensions (Intel SGX) Enclaves",
  url =          "https://www.intel.com/content/dam/develop/external/us/en/documents/enclave-measurement-tool-intel-sgx-737361.pdf"
}

@online{vedb,
  author =       "ByteDance Volcengine",
  year =         "2022",
  title =        "Cloud database for veDB",
  url =          "https://www.volcengine.com/product/vedb-mysql"
}

@article{kaplan2016amd,
  title={AMD memory encryption},
  author={Kaplan, David and Powell, Jeremy and Woller, Tom},
  journal={White paper},
  year={2016}
}

@article{voigt2017eu,
  title={The eu general data protection regulation (gdpr)},
  author={Voigt, Paul and Von dem Bussche, Axel},
  journal={A Practical Guide, 1st Ed., Cham: Springer International Publishing},
  volume={10},
  number={3152676},
  pages={10--5555},
  year={2017},
  publisher={Springer}
}

@article{pardau2018california,
  title={The California consumer privacy act: Towards a European-style privacy regime in the United States},
  author={Pardau, Stuart L},
  journal={J. Tech. L. \& Pol'y},
  volume={23},
  pages={68},
  year={2018},
  publisher={HeinOnline}
}

@article{poddar2016arx,
  title={Arx: an encrypted database using semantically secure encryption},
  author={Poddar, Rishabh and Boelter, Tobias and Popa, Raluca Ada},
  journal={Cryptology ePrint Archive},
  year={2016}
}

@article{tu2013processing,
  title={Processing analytical queries over encrypted data},
  author={Tu, Stephen Lyle and Kaashoek, M Frans and Madden, Samuel R and Zeldovich, Nickolai},
  year={2013},
  publisher={Association for Computing Machinery (ACM)}
}

@inproceedings{hacigumucs2002executing,
  title={Executing SQL over encrypted data in the database-service-provider model},
  author={Hacig{\"u}m{\"u}{\c{s}}, Hakan and Iyer, Bala and Li, Chen and Mehrotra, Sharad},
  booktitle={Proceedings of the 2002 ACM SIGMOD international conference on Management of data},
  pages={216--227},
  year={2002}
}

@online{aurora,
  author =       "Amazon Web Services",
  year =         "2022",
  title =        "Encrypting Amazon Aurora resources",
  url =          "https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/Overview.Encryption.html"
}

@inproceedings{arasu2013orthogonal,
  title={Orthogonal Security with Cipherbase.},
  author={Arasu, Arvind and Blanas, Spyros and Eguro, Ken and Kaushik, Raghav and Kossmann, Donald and Ramamurthy, Ravishankar and Venkatesan, Ramarathnam},
  booktitle={CIDR},
  year={2013}
}

@article{vinayagamurthy2019stealthdb,
  title={StealthDB: a Scalable Encrypted Database with Full SQL Query Support.},
  author={Vinayagamurthy, Dhinakaran and Gribov, Alexey and Gorbunov, Sergey},
  journal={Proc. Priv. Enhancing Technol.},
  volume={2019},
  number={3},
  pages={370--388},
  year={2019}
}

@article{yang2023vedb,
  title={VeDB: A Software and Hardware Enabled Trusted Relational Database},
  author={Yang, Xinying and Zhang, Ruide and Yue, Cong and Liu, Yang and Ooi, Beng Chin and Gao, Qun and Zhang, Yuan and Yang, Hao},
  journal={Proceedings of the ACM on Management of Data},
  volume={1},
  number={2},
  pages={1--27},
  year={2023},
  publisher={ACM New York, NY, USA}
}

@inproceedings{priebe2018enclavedb,
  title={EnclaveDB: A secure database using SGX},
  author={Priebe, Christian and Vaswani, Kapil and Costa, Manuel},
  booktitle={2018 IEEE Symposium on Security and Privacy (SP)},
  pages={264--278},
  year={2018},
  organization={IEEE}
}

@article{nakamoto2008bitcoin,
  title={Bitcoin: A peer-to-peer electronic cash system},
  author={Nakamoto, Satoshi},
  journal={Decentralized Business Review},
  url={https://bitcoin.org/bitcoin.pdf},
  year={2008}
}

@inproceedings{wang2022rt,
  title={RT-TEE: Real-time System Availability for Cyber-physical Systems using ARM TrustZone},
  author={Wang, Jinwen and Li, Ao and Li, Haoran and Lu, Chenyang and Zhang, Ning},
  booktitle={2022 IEEE Symposium on Security and Privacy (SP)},
  pages={1573--1573},
  year={2022},
  organization={IEEE Computer Society}
}

@article{cen2017trusted,
  title={Trusted time and monotonic counters with intel software guard extensions platform services},
  author={Cen, Shanwei and Zhang, Bo},
  journal={Online at: https://software. intel. com/sites/default/files/managed/1b/a2/Intel-SGX-Platform-Services. pdf},
  year={2017}
}

@article{weisse2017regaining,
  title={Regaining lost cycles with HotCalls: A fast interface for SGX secure enclaves},
  author={Weisse, Ofir and Bertacco, Valeria and Austin, Todd},
  journal={ACM SIGARCH Computer Architecture News},
  volume={45},
  number={2},
  pages={81--93},
  year={2017},
  publisher={ACM New York, NY, USA}
}

@incollection{merkle2019protocols,
  title={Protocols for public key cryptosystems},
  author={Merkle, Ralph C},
  booktitle={Secure communications and asymmetric cryptosystems},
  pages={73--104},
  year={2019},
  publisher={Routledge}
}

@article{buterin2014next,
  title={A next-generation smart contract and decentralized application platform},
  author={Buterin, Vitalik and others},
  journal={white paper},
  volume={3},
  number={37},
  pages={2--1},
  year={2014}
}

@inproceedings{androulaki2018hyperledger,
  title={Hyperledger fabric: a distributed operating system for permissioned blockchains},
  author={Androulaki, Elli and Barger, Artem and Bortnikov, Vita and Cachin, Christian and Christidis, Konstantinos and De Caro, Angelo and Enyeart, David and Ferris, Christopher and Laventman, Gennady and Manevich, Yacov and others},
  booktitle={Proceedings of the thirteenth EuroSys conference},
  pages={1--15},
  year={2018}
}

@article{hearn2016corda,
  title={Corda: A distributed ledger},
  author={Hearn, Mike and Brown, Richard Gendal},
  journal={Corda Technical White Paper},
  volume={2016},
  url={https://www.corda.net/content/corda-technical-whitepaper.pdf},
  year={2016}
}

@article{morgan2016quorum,
  title={Quorum whitepaper},
  author={Morgan, JP},
  journal={New York: JP Morgan Chase},
  year={2016}
}

@online{qldb,
  title={ Amazon quantum ledger database (qldb)},
  author={AWS},
  url={https://aws.amazon.com/qldb},
  year={2018}
}

@online{gartner,
  title={Gartner Top 10 Trends in Data and Analytics for 2020},
  author={Gartner},
  url={https://www.gartner.com/smarterwithgartner/gartner-top-10-trends-in-data-and-analytics-for-2020},
  year={2020}
}

@article{yang2020ledgerdb,
  title={LedgerDB: A centralized ledger database for universal audit and verification},
  author={Yang, Xinying and Zhang, Yuan and Wang, Sheng and Yu, Benquan and Li, Feifei and Li, Yize and Yan, Wenyuan},
  journal={Proceedings of the VLDB Endowment},
  volume={13},
  number={12},
  pages={3138--3151},
  year={2020},
  publisher={VLDB Endowment}
}

@online{provendb,
  title={Provendb: A blockchain enabled database service},
  author={Provendb},
  url={https://provendb.com/litepaper},
  year={2020}
}

@online{oracleblockchain,
  title={Blockchain Tables in Oracle Database 21c},
  author={Oracle},
  url={https://oracle-base.com/articles/21c/blockchain-tables-21c},
  year={2022}
}

@inproceedings{antonopoulos2021sql,
  title={SQL Ledger: Cryptographically Verifiable Data in Azure SQL Database},
  author={Antonopoulos, Panagiotis and Kaushik, Raghav and Kodavalla, Hanuma and Rosales Aceves, Sergio and Wong, Reilly and Anderson, Jason and Szymaszek, Jakub},
  booktitle={Proceedings of the 2021 International Conference on Management of Data},
  pages={2437--2449},
  year={2021}
}

@article{snow2014business,
  title={Business processes secured by immutable audit trails on the blockchain},
  author={Snow, Paul and Deery, Brian and Lu, Jack and Johnston, David and Kirby, Peter and Sprague, Andrew Yashchuk and Byington, Dustin},
  journal={Brave New Coin},
  year={2014}
}

@inproceedings{tamassia2003authenticated,
  title={Authenticated data structures},
  author={Tamassia, Roberto},
  booktitle={European symposium on algorithms},
  pages={2--5},
  year={2003},
  organization={Springer}
}

@article{martel2004general,
  title={A general model for authenticated data structures},
  author={Martel, Charles and Nuckolls, Glen and Devanbu, Premkumar and Gertz, Michael and Kwong, April and Stubblebine, Stuart G},
  journal={Algorithmica},
  volume={39},
  number={1},
  pages={21--41},
  year={2004},
  publisher={Springer}
}

@article{miller2014authenticated,
  title={Authenticated data structures, generically},
  author={Miller, Andrew and Hicks, Michael and Katz, Jonathan and Shi, Elaine},
  journal={ACM SIGPLAN Notices},
  volume={49},
  number={1},
  pages={411--423},
  year={2014},
  publisher={ACM New York, NY, USA}
}


@article{bajaj2013trusteddb,
  title={TrustedDB: A trusted hardware-based database with privacy and data confidentiality},
  author={Bajaj, Sumeet and Sion, Radu},
  journal={IEEE Transactions on Knowledge and Data Engineering},
  volume={26},
  number={3},
  pages={752--765},
  year={2013},
  publisher={IEEE}
}


@article{zhang2020spitz,
  title={Spitz: A Verifiable Database System},
  author={Zhang, Meihui and Xie, Zhongle and Yue, Cong and Zhong, Ziyue},
  journal={Proceedings of the VLDB Endowment},
  volume={13},
  number={12},
  year={2020}
}

@inproceedings{arasu2013secure,
  title={Secure database-as-a-service with cipherbase},
  author={Arasu, Arvind and Blanas, Spyros and Eguro, Ken and Joglekar, Manas and Kaushik, Raghav and Kossmann, Donald and Ramamurthy, Ravi and Upadhyaya, Prasang and Venkatesan, Ramarathnam},
  booktitle={Proceedings of the 2013 ACM SIGMOD International Conference on Management of Data},
  pages={1033--1036},
  year={2013}
}

@inproceedings{popa2011cryptdb,
  title={CryptDB: protecting confidentiality with encrypted query processing},
  author={Popa, Raluca Ada and Redfield, Catherine MS and Zeldovich, Nickolai and Balakrishnan, Hari},
  booktitle={Proceedings of the twenty-third ACM symposium on operating systems principles},
  pages={85--100},
  year={2011}
}


@inproceedings{backes2013verifiable,
  title={Verifiable delegation of computation on outsourced data},
  author={Backes, Michael and Fiore, Dario and Reischuk, Raphael M},
  booktitle={Proceedings of the 2013 ACM SIGSAC conference on Computer \& communications security},
  pages={863--874},
  year={2013}
}

@article{bajaj2013correctdb,
  title={CorrectDB: SQL engine with practical query authentication},
  author={Bajaj, Sumeet and Sion, Radu},
  journal={Proceedings of the VLDB Endowment},
  volume={6},
  number={7},
  pages={529--540},
  year={2013},
  publisher={VLDB Endowment}
}

@article{sinha2018veritasdb,
  title={Veritasdb: High throughput key-value store with integrity},
  author={Sinha, Rohit and Christodorescu, Mihai},
  journal={Cryptology ePrint Archive},
  year={2018}
}

@inproceedings{xu2019vchain,
  title={vchain: Enabling verifiable boolean range queries over blockchain databases},
  author={Xu, Cheng and Zhang, Ce and Xu, Jianliang},
  booktitle={Proceedings of the 2019 international conference on management of data},
  pages={141--158},
  year={2019}
}

@inproceedings{kim2019shieldstore,
  title={Shieldstore: Shielded in-memory key-value storage with sgx},
  author={Kim, Taehoon and Park, Joongun and Woo, Jaewook and Jeon, Seungheun and Huh, Jaehyuk},
  booktitle={Proceedings of the Fourteenth EuroSys Conference 2019},
  pages={1--15},
  year={2019}
}

@article{mohan2020merkle,
  title={Merkle tree and Blockchain-based cloud data auditing},
  author={Mohan, Arun Prasad and Gladston, Angelin and others},
  journal={International Journal of Cloud Applications and Computing (IJCAC)},
  volume={10},
  number={3},
  pages={54--66},
  year={2020},
  publisher={IGI Global}
}

@inproceedings{libra1,
  title={The Libra Blockchain},
  author={Zachary Amsden et al},
  journal={The Libra  Association},
  url={https://mitsloan.mit.edu/shared/ods/documents?PublicationDocumentID=5859},
  year={2019}
}

@online{mpt,
  title={PATRICIA MERKLE TREES},
  author={The Ethereum Association},
  url={https://ethereum.org/developers/docs/data-structures-and-encoding/patricia-merkle-trie/},
  year={2022}
}

@inproceedings{castro1999practical,
  title={Practical byzantine fault tolerance},
  author={Castro, Miguel and Liskov, Barbara and others},
  booktitle={OSDI},
  volume={99},
  number={1999},
  pages={173--186},
  year={1999}
}

@inproceedings{ongaro2014search,
  title={In search of an understandable consensus algorithm},
  author={Ongaro, Diego and Ousterhout, John},
  booktitle={2014 USENIX Annual Technical Conference (Usenix ATC 14)},
  pages={305--319},
  year={2014}
}

@online{oracleblockchain1,
  title={Why Oracle Implement Blockchain in the Database},
  author={SQL Maria},
  url={https://sqlmaria.com/2021/03/03/why-oracle-implement-blockchain-in-the-database},
  year={2021}
}

@article{mcconaghy2016bigchaindb,
  title={Bigchaindb: a scalable blockchain database},
  author={McConaghy, Trent and Marques, Rodolphe and M{\"u}ller, Andreas and De Jonghe, Dimitri and McConaghy, Troy and McMullen, Greg and Henderson, Ryan and Bellemare, Sylvain and Granzotto, Alberto},
  journal={white paper, BigChainDB},
  year={2016}
}

@article{el2019blockchaindb,
  title={BlockchainDB: A shared database on blockchains},
  author={El-Hindi, Muhammad and Binnig, Carsten and Arasu, Arvind and Kossmann, Donald and Ramamurthy, Ravi},
  journal={Proceedings of the VLDB Endowment},
  volume={12},
  number={11},
  pages={1597--1609},
  year={2019},
  publisher={VLDB Endowment}
}

@inproceedings{senthil2019blockchain,
  title={Blockchain meets database: design and implementation of a blockchain relational database [J]},
  author={Senthil, Nathan and Chander, Govindarajan and Adarsh, Saraf and others},
  booktitle={Proceedings of the VLDB Endowment},
  volume={12},
  number={11},
  pages={1539--1552},
  year={2019}
}

@techreport{adams2001internet,
  title={Internet X. 509 public key infrastructure time-stamp protocol (TSP)},
  author={Adams, Carlisle and Cain, Pat and Pinkas, Denis and Zuccherato, Robert},
  year={2001}
}

@inproceedings{van2021cacheout,
  title={CacheOut: Leaking data on Intel CPUs via cache evictions},
  author={van Schaik, Stephan and Minkin, Marina and Kwong, Andrew and Genkin, Daniel and Yarom, Yuval},
  booktitle={2021 IEEE Symposium on Security and Privacy (SP)},
  pages={339--354},
  year={2021},
  organization={IEEE}
}

@online{rightstobeforgotten,
  author =       "The Horizon 2020 Framework Programme of the European Union",
  year =         "2020",
  title =        "Everything you need to know about the Right to be forgotten",
  url =          "https://gdpr.eu/right-to-be-forgotten"
}

@inproceedings{ferraiolo1995role,
  title={Role-based access control (RBAC): Features and motivations},
  author={Ferraiolo, David and Cugini, Janet and Kuhn, D Richard and others},
  booktitle={Proceedings of 11th annual computer security application conference},
  pages={241--48},
  year={1995}
}

@techreport{bider2012sha,
  title={SHA-2 data integrity verification for the secure shell (SSH) transport layer protocol},
  author={Bider, D and Baushke, M},
  year={2012}
}

@online{brin,
  author =       "Wikimedia",
  year =         "2021",
  title =        "Block Range Index",
  url =          "https://en.wikipedia.org/wiki/Block_Range_Index"
}

@article{rivest1978method,
  title={A method for obtaining digital signatures and public-key cryptosystems},
  author={Rivest, Ronald L and Shamir, Adi and Adleman, Leonard},
  journal={Communications of the ACM},
  volume={21},
  number={2},
  pages={120--126},
  year={1978},
  publisher={ACM New York, NY, USA}
}

@inproceedings{matetic2019bite,
  title={$\{$BITE$\}$: Bitcoin lightweight client privacy using trusted execution},
  author={Matetic, Sinisa and W{\"u}st, Karl and Schneider, Moritz and Kostiainen, Kari and Karame, Ghassan and Capkun, Srdjan},
  booktitle={28th USENIX Security Symposium (USENIX Security 19)},
  pages={783--800},
  year={2019}
}

@online{vecloud,
  author =       "ByteDance",
  year =         "2022",
  title =        "Volcano Engine",
  url =          "https://www.volcengine.com"
}


@online{hangzhou,
  author =       "Wikepedia",
  year =         "2022",
  title =        "Hangzhou Internet Court",
  url =          "https://en.wikipedia.org/wiki/Hangzhou_Internet_Court"
}

@online{bjcourt,
  author =       "Beijing Internet Court",
  year =         "2023",
  title =        "Beijing Internet Court",
  url =          "https://english.bjinternetcourt.gov.cn"
}

@online{shrubs,
  author =       "Alex, Gluchowski and Kobi, Gurkan and Marek, Olszewski and Eran, Tromer and Alexander, Vlasov",
  year =         "2019",
  title =        "Shrubs - A New Gas Efficient Privacy Protocol",
  url =          "https://archive.devcon.org/archive/watch/5/shrubs-a-new-gas-efficient-privacy-protocol/?tab=YouTube"
}

@inproceedings{yang2022ubiquitous,
  title={Ubiquitous Verification in Centralized Ledger Database},
  author={Yang, Xinying and Wang, Sheng and Li, Feifei and Zhang, Yuan and Yan, Wenyuan and Gai, Fangyu and Yu, Benquan and Feng, Likai and Gao, Qun and Li, Yize},
  booktitle={2022 IEEE 38th International Conference on Data Engineering (ICDE)},
  pages={1808--1821},
  year={2022},
  organization={IEEE}
}

@inproceedings{bellare2007deterministic,
  title={Deterministic and efficiently searchable encryption},
  author={Bellare, Mihir and Boldyreva, Alexandra and O’Neill, Adam},
  booktitle={Annual International Cryptology Conference},
  pages={535--552},
  year={2007},
  organization={Springer}
}

@inproceedings{rivest1983randomized,
  title={Randomized encryption techniques},
  author={Rivest, Ronald L and Sherman, Alan T},
  booktitle={Advances in Cryptology},
  pages={145--163},
  year={1983},
  organization={Springer}
}

@online{ebcdic,
  author =       "IBM",
  year =         "2021",
  title =        "CODEPAGE option syntax",
  url =          "https://www.ibm.com/docs/en/cobol-zos/6.3?topic=options-codepage"
}

@inproceedings{oleksenko2018varys,
  title={Varys: Protecting $\{$SGX$\}$ Enclaves from Practical $\{$Side-Channel$\}$ Attacks},
  author={Oleksenko, Oleksii and Trach, Bohdan and Krahn, Robert and Silberstein, Mark and Fetzer, Christof},
  booktitle={2018 USENIX Annual Technical Conference (USENIX ATC 18)},
  pages={227--240},
  year={2018}
}

@inproceedings{weiser2019timber,
  title={Timber-v: Tag-isolated memory bringing fine-grained enclaves to risc-v.},
  author={Weiser, Samuel and Werner, Mario and Brasser, Ferdinand and Malenko, Maja and Mangard, Stefan and Sadeghi, Ahmad-Reza},
  booktitle={NDSS},
  year={2019}
}

@article{intel2020software,
  title={Software guard extensions sdk developer reference for linux* os},
  howpublished = "\url{https://www.intel.com/content/www/us/en/developer/tools/software-guard-extensions/linux-overview.html}",
  author={Intel, R},
  year={2020}
}


@article{o1996log,
  title={The log-structured merge-tree (LSM-tree)},
  author={O’Neil, Patrick and Cheng, Edward and Gawlick, Dieter and O’Neil, Elizabeth},
  journal={Acta Informatica},
  volume={33},
  number={4},
  pages={351--385},
  year={1996},
  publisher={Springer}
}

@misc{edery_sharma_2020, title={OCP Security Announces version 1.0 specs for Root of Trust}, url={https://www.opencompute.org/blog/ocp-security-announces-version-10-specs-for-root-of-trust}, journal={Open Compute Project}, publisher={Open Compute Project}, author={Edery, Yigal and Sharma, Rajeev}, year={2020}, month={Nov}} 


@article{beniiche2020study,
  title={A study of blockchain oracles},
  author={Beniiche, Abdeljalil},
  journal={arXiv preprint arXiv:2004.07140},
  year={2020}
}

@online{awsblockchain,
  title={ Amazon Managed Blockchain},
  author={AWS},
  url={https://aws.amazon.com/blockchain},
  year={2022}
}

@online{aliblockchain,
  title={ Alibaba Blockchain as a Service},
  author={Alibaba Cloud},
  url={https://www.alibabacloud.com/product/baas},
  year={2022}
}

@online{ibmblockchain,
  title={IBM Blockchain},
  author={IBM},
  url={https://www.ibm.com/blockchain},
  year={2022}
}

@inproceedings{cao2020characterizing,
  title={Characterizing, Modeling, and Benchmarking $\{$RocksDB$\}$$\{$Key-Value$\}$ Workloads at Facebook},
  author={Cao, Zhichao and Dong, Siying and Vemuri, Sagar and Du, David HC},
  booktitle={18th USENIX Conference on File and Storage Technologies (FAST 20)},
  pages={209--223},
  year={2020}
}

@misc{ghemawat2014leveldb,
  title={Leveldb is a fast key-value storage library written at google that provides an ordered mapping from string keys to string values},
  author={Ghemawat, Sanjay and Dean, Jeff},
  journal={2011-08-10)(2020-03-26). https://github. com/google/leveldb},
  year={2014}
}

@misc{yang2020blockchain,
  title={Blockchain-based music originality analysis method and apparatus},
  author={Yang, Xinying},
  year={2020},
  month=apr # "~21",
  publisher={Google Patents},
  note={US Patent 10,628,485}
}

@online{tiktok,
  title={Make Your Day - TikTok},
  author={ByteDance},
  url={https://www.tiktok.com/},
  year={2023}
}

@book{joyce2020copyright,
  title={Copyright law},
  author={Joyce, Craig and Ochoa, Tyler T and Carroll, Michael W},
  year={2020},
  publisher={Carolina Academic Press}
}

@online{bamt,
  title={Ledger Database},
  author={National University of Singapore},
  url={https://github.com/nusdbsystem/LedgerDatabase},
  year={2022}
}

@article{kuszmaul2019verkle,
  title={Verkle trees},
  author={Kuszmaul, John},
  journal={Verkle Trees},
  volume={1},
  year={2019}
}

@online{volcengine,
  author =       "ByteDance",
  year =         "2023",
  title =        "Volcengine",
  url =          "https://github.com/volcengine"
}

@inproceedings{sheng2008verifiable,
  title={Verifiable privacy-preserving range query in two-tiered sensor networks},
  author={Sheng, Bo and Li, Qun},
  booktitle={IEEE INFOCOM 2008-The 27th Conference on Computer Communications},
  pages={46--50},
  year={2008},
  organization={IEEE}
}

@inproceedings{zhang2017vsql,
  title={vSQL: Verifying arbitrary SQL queries over dynamic outsourced databases},
  author={Zhang, Yupeng and Genkin, Daniel and Katz, Jonathan and Papadopoulos, Dimitrios and Papamanthou, Charalampos},
  booktitle={2017 IEEE Symposium on Security and Privacy (SP)},
  pages={863--880},
  year={2017},
  organization={IEEE}
}


@online{tpcc,
  title={TPC-C Benchmark},
  author={TPC},
  url={https://www.tpc.org/tpcc},
  year={2022}
}

@online{tpch,
  title={TPC-H Benchmark},
  author={TPC},
  url={https://www.tpc.org/tpch},
  year={2022}
}

@inproceedings{concerto,
  author    = {Arvind Arasu and
               Ken Eguro and
               Raghav Kaushik and
               Donald Kossmann and
               Pingfan Meng and
               Vineet Pandey and
               Ravi Ramamurthy},
  title     = {Concerto: {A} High Concurrency Key-Value Store with Integrity},
  booktitle = {SIGMOD},
  pages     = {251--266},
  year      = {2017}
}

@article{veritasdb,
  author    = {Rohit Sinha and
               Mihai Christodorescu},
  title     = {VeritasDB: High Throughput Key-Value Store with Integrity},
  journal   = {IACR},
  volume    = {2018},
  pages     = {251},    
  year      = {2018}
}

@online{sysbench,
  author =       "Sysbench",
  year =         "2023",
  title =        "Scriptable database and system performance benchmark",
  url =          "https://github.com/akopytov/sysbench"
}


@misc{siteintel,
  title={Intel Trust Domain Extensions (Intel TDX)},
  year="2023",
  author={Site, Intel Developer}
}

@article{sev2020strengthening,
  title={Strengthening VM isolation with integrity protection and more},
  author={Sev-Snp, AMD},
  journal={White Paper, January},
  volume={53},
  pages={1450--1465},
  year={2020}
}

@article{kollendageneral,
  title={General overview of AMD SEV-SNP and Intel TDX},
  year="2023",
  author={Kollenda, Kevin}
}

@inproceedings{ekberg2013trusted,
  title={Trusted execution environments on mobile devices},
  author={Ekberg, Jan-Erik and Kostiainen, Kari and Asokan, Nadarajah},
  booktitle={Proceedings of the 2013 ACM SIGSAC conference on Computer \& communications security},
  pages={1497--1498},
  year={2013}
}

@article{jauernig2020trusted,
  title={Trusted execution environments: properties, applications, and challenges},
  author={Jauernig, Patrick and Sadeghi, Ahmad-Reza and Stapf, Emmanuel},
  journal={IEEE Security \& Privacy},
  volume={18},
  number={2},
  pages={56--60},
  year={2020},
  publisher={IEEE}
}

@article{aumasson2008sha,
  title={Sha-3 proposal blake},
  author={Aumasson, Jean-Philippe and Henzen, Luca and Meier, Willi and Phan, Raphael C-W},
  journal={Submission to NIST},
  volume={92},
  pages={1--79},
  year={2008}
}

@online{mysqlencrypted,
  title={Encryption and Compression Functions},
  author={Oracle},
  url={https://dev.mysql.com/doc/refman/8.0/en/encryption-functions.html},
  year={2023}
}

@book{date2014time,
  title={Time and relational theory: temporal databases in the relational model and SQL},
  author={Date, Christopher John and Darwen, Hugh and Lorentzos, Nikos},
  year={2014},
  publisher={Morgan Kaufmann}
}

@article{glassdb,
  author = {Yue, Cong and Dinh, Tien Tuan Anh and Xie, Zhongle and Zhang, Meihui and Chen, Gang and Ooi, Beng Chin and Xiao, Xiaokui},
  title = {GlassDB: An Efficient Verifiable Ledger Database System Through Transparency},
  year = {2023},
  volume = {16},
  number = {6},
  journal = {PVLDB},
  pages = {1359–1371}
}

@article{veribench,
  author = {Yue, Cong and Zhang, Meihui and Zhu, Changhao and Chen, Gang and Loghin, Dumitrel and Ooi, Beng Chin},
  title = {VeriBench: Analyzing the Performance of Database Systems with Verifiability},
  year = {2023},
  volume = {16},
  number = {9},
  journal = {PVLDB},
  pages = {2145--2157}
}

@online{lark,
  title={Make everything a breeze},
  author={ByteDance},
  url={https://www.larksuite.com/},
  year={2023}
}
