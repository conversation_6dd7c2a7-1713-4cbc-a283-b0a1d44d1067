
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee sgx

@incollection{mckeen2016intel,
  title={Intel{\textregistered} software guard extensions (intel{\textregistered} sgx) support for dynamic memory management inside an enclave},
  author={<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>tai and C<PERSON>pi, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON>},
  booktitle={Proceedings of the Hardware and Architectural Support for Security and Privacy 2016},
  pages={1--9},
  year={2016}
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee tdx

@article{cheng2023intel,
  title={Intel TDX Demystified: A Top-Down Approach},
  author={<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>hong<PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>},
  journal={arXiv preprint arXiv:2303.15540},
  year={2023}
}



@inproceedings{sahita2021security,
  title={Security analysis of confidential-compute instruction set architecture for virtualized workloads},
  author={Sahita, Ravi and Caspi, Dror and Huntley, Barry and Scarlata, Vincent and Chaikin, Baruch and Chhabra, Siddhartha and Aharon, Arie and Ouziel, Ido},
  booktitle={2021 International Symposium on Secure and Private Execution Environment Design (SEED)},
  pages={121--131},
  year={2021},
  organization={IEEE}
}


@article{sardar2023sok,
  title={SoK: Attestation in confidential computing},
  author={Sardar, M and Fossati, Thomas and Frost, Simon},
  journal={ResearchGate pre-print},
  year={2023}
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee A-TLS

@article{knauth2018integrating,
  title={Integrating remote attestation with transport layer security},
  author={Knauth, Thomas and Steiner, Michael and Chakrabarti, Somnath and Lei, Li and Xing, Cedric and Vij, Mona},
  journal={arXiv preprint arXiv:1801.05863},
  year={2018}
}




%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee trustzone
@misc{arm2004trustzone,
  title={TrustZone Technology},
  author={ARM, ARM},
  year={2004}
}




%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee cca
@inproceedings{zhang2023shelter,
  title={SHELTER: Extending Arm CCA with Isolation in User Space},
  author={Zhang, Yiming and Hu, Yuxin and Ning, Zhenyu and Zhang, Fengwei and Luo, Xiapu and Huang, Haoyang and Yan, Shoumeng and He, Zhengyu},
  booktitle={32nd USENIX Security Symposium (USENIX Security’23)},
  year={2023}
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee sev

@article{kaplan2016amd,
  title={AMD memory encryption},
  author={Kaplan, David and Powell, Jeremy and Woller, Tom},
  journal={White paper},
  pages={13},
  year={2016}
}




%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee sev-snp


@article{sev2020strengthening,
  title={Strengthening VM isolation with integrity protection and more},
  author={Sev-Snp, AMD},
  journal={White Paper, January},
  volume={53},
  pages={1450--1465},
  year={2020}
}



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee runtime

@inproceedings{tsai2017graphene,
  title={Graphene-SGX: A Practical Library OS for Unmodified Applications on SGX},
  author={Tsai, Chia-Che and Porter, Donald E and Vij, Mona},
  booktitle={2017 USENIX Annual Technical Conference (USENIX ATC 17)},
  pages={645--658},
  year={2017}
}


@inproceedings{lee2020keystone,
  title={Keystone: An open framework for architecting trusted execution environments},
  author={Lee, Dayeol and Kohlbrenner, David and Shinde, Shweta and Asanovi{\'c}, Krste and Song, Dawn},
  booktitle={Proceedings of the Fifteenth European Conference on Computer Systems},
  pages={1--16},
  year={2020}
}

@inproceedings{shen2020occlum,
  title={Occlum: Secure and efficient multitasking inside a single enclave of intel sgx},
  author={Shen, Youren and Tian, Hongliang and Chen, Yu and Chen, Kang and Wang, Runji and Xu, Yi and Xia, Yubin and Yan, Shoumeng},
  booktitle={Proceedings of the Twenty-Fifth International Conference on Architectural Support for Programming Languages and Operating Systems},
  pages={955--970},
  year={2020}
}



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee db

@inproceedings{priebe2018enclavedb,
  title={EnclaveDB: A secure database using SGX},
  author={Priebe, Christian and Vaswani, Kapil and Costa, Manuel},
  booktitle={2018 IEEE Symposium on Security and Privacy (SP)},
  pages={264--278},
  year={2018},
  organization={IEEE}
}


@article{vinayagamurthy2019stealthdb,
  title={StealthDB: a Scalable Encrypted Database with Full SQL Query Support.},
  author={Vinayagamurthy, Dhinakaran and Gribov, Alexey and Gorbunov, Sergey},
  journal={Proc. Priv. Enhancing Technol.},
  volume={2019},
  number={3},
  pages={370--388},
  year={2019}
}

@inproceedings{li2023encrypted,
  title={Encrypted Databases Made Secure Yet Maintainable},
  author={Li, Mingyu and Zhao, Xuyang and Chen, Le and Tan, Cheng and Li, Huorong and Wang, Sheng and Mi, Zeyu and Xia, Yubin and Li, Feifei and Chen, Haibo},
  booktitle={17th USENIX Symposium on Operating Systems Design and Implementation (OSDI 23)},
  pages={117--133},
  year={2023}
}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee gpu


@inproceedings{volos2018graviton,
  title={Graviton: Trusted execution environments on $\{$GPUs$\}$},
  author={Volos, Stavros and Vaswani, Kapil and Bruno, Rodrigo},
  booktitle={13th USENIX Symposium on Operating Systems Design and Implementation (OSDI 18)},
  pages={681--696},
  year={2018}
}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee tpm

@article{wang2023svtpm,
  title={SvTPM: SGX-based Virtual Trusted Platform Modules for Cloud Computing},
  author={Wang, Juan and Wang, Jie and Fan, Chengyang and Yan, Fei and Cheng, Yueqiang and Zhang, Yinqian and Zhang, Wenhui and Yang, Mengda and Hu, Hongxin},
  journal={IEEE Transactions on Cloud Computing},
  year={2023},
  publisher={IEEE}
}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee network

@article{wang2020s,
  title={S-blocks: Lightweight and trusted virtual security function with SGX},
  author={Wang, Juan and Hao, Shirong and Hu, Hongxin and Zhao, Bo and Li, Hongda and Zhang, Wenhui and Xu, Jun and Liu, Peng and Ma, Jing},
  journal={IEEE Transactions on Cloud Computing},
  volume={10},
  number={2},
  pages={1082--1099},
  year={2020},
  publisher={IEEE}
}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee mktme


@article{sardar2021demystifying,
  title={Demystifying attestation in intel trust domain extensions via formal verification},
  author={Sardar, Muhammad Usama and Musaev, Saidgani and Fetzer, Christof},
  journal={IEEE access},
  volume={9},
  pages={83067--83079},
  year={2021},
  publisher={IEEE}
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%  sgx2 comparison with sgx1
@inproceedings{sgx2_benchmark,
author = {El-Hindi, Muhammad and Ziegler, Tobias and Heinrich, Matthias and Lutsch, Adrian and Zhao, Zheguang and Binnig, Carsten},
title = {Benchmarking the Second Generation of Intel SGX Hardware},
year = {2022},
isbn = {9781450393782},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/3533737.3535098},
doi = {10.1145/3533737.3535098},
abstract = {In recent years, trusted execution environments (TEEs) such as Intel Software Guard Extensions (SGX) have gained a lot of attention in the database community. This is because TEEs provide an interesting platform for building trusted databases in the cloud. However, until recently SGX was only available on low-end single socket servers built on the Intel Xeon E3 processor generation and came with many restrictions for building DBMSs. With the availability of the new Ice Lake processors, Intel provides a new implementation of the SGX technology that supports high-end multi-socket servers. With this new implementation, which we refer to as SGXv2 in this paper, Intel promises to address several limitations of SGX enclaves. This raises the question whether previous efforts to overcome the limitations of SGX for DBMSs are still applicable and if the new generation of SGX can truly deliver on the promise to secure data without compromising on performance. To answer this question, in this paper we conduct a first systematic performance study of Intel SGXv2 and compare it to the previous generation of SGX.},
booktitle = {Proceedings of the 18th International Workshop on Data Management on New Hardware},
articleno = {5},
numpages = {8},
location = {Philadelphia, PA, USA},
series = {DaMoN '22}
}

@inproceedings{vllm,
  title={Efficient memory management for large language model serving with pagedattention},
  author={Kwon, Woosuk and Li, Zhuohan and Zhuang, Siyuan and Sheng, Ying and Zheng, Lianmin and Yu, Cody Hao and Gonzalez, Joseph and Zhang, Hao and Stoica, Ion},
  booktitle={Proceedings of the 29th Symposium on Operating Systems Principles},
  year={2023}
}

@article{sglang,
  title={Efficiently programming large language models using sglang},
  author={Zheng, Lianmin and Yin, Liangsheng and Xie, Zhiqiang and Huang, Jeff and Sun, Chuyue and Yu, Cody Hao and Cao, Shiyi and Kozyrakis, Christos and Stoica, Ion and Gonzalez, Joseph E and others},
  journal={arXiv preprint arXiv:2312.07104},
  year={2023}
}

@inproceedings{lightLLM,
  title={A tensor compiler for unified machine learning prediction serving},
  author={Nakandala, Supun and Saur, Karla and Yu, Gyeong-In and Karanasos, Konstantinos and Curino, Carlo and Weimer, Markus and Interlandi, Matteo},
  booktitle={14th USENIX Symposium on Operating Systems Design and Implementation (OSDI 20)},
  year={2020}
}

@inproceedings{deepspeed,
  title={Deepspeed: System optimizations enable training deep learning models with over 100 billion parameters},
  author={Rasley, Jeff and Rajbhandari, Samyam and Ruwase, Olatunji and He, Yuxiong},
  booktitle={Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery \& Data Mining},
  year={2020}
}

@article{attention,
  title={Attention is all you need},
  author={Vaswani, Ashish and Shazeer, Noam and Parmar, Niki and Uszkoreit, Jakob and Jones, Llion and Gomez, Aidan N and Kaiser, {\L}ukasz and Polosukhin, Illia},
  journal={Advances in neural information processing systems},
  volume={30},
  year={2017}
}

@article{pope2023efficiently,
  title={Efficiently scaling transformer inference},
  author={Pope, Reiner and Douglas, Sholto and Chowdhery, Aakanksha and Devlin, Jacob and Bradbury, James and Heek, Jonathan and Xiao, Kefan and Agrawal, Shivani and Dean, Jeff},
  journal={Proceedings of Machine Learning and Systems},
  volume={5},
  year={2023}
}

@article{llama,
  title={Llama: Open and efficient foundation language models},
  author={Touvron, Hugo and Lavril, Thibaut and Izacard, Gautier and Martinet, Xavier and Lachaux, Marie-Anne and Lacroix, Timoth{\'e}e and Rozi{\`e}re, Baptiste and Goyal, Naman and Hambro, Eric and Azhar, Faisal and others},
  journal={arXiv preprint arXiv:2302.13971},
  year={2023}
}

@article{gpt,
  title={Language models are few-shot learners},
  author={Brown, Tom and Mann, Benjamin and Ryder, Nick and Subbiah, Melanie and Kaplan, Jared D and Dhariwal, Prafulla and Neelakantan, Arvind and Shyam, Pranav and Sastry, Girish and Askell, Amanda and others},
  journal={Advances in neural information processing systems},
  year={2020}
}

@article{ye2024chunkattention,
  title={ChunkAttention: Efficient Self-Attention with Prefix-Aware KV Cache and Two-Phase Partition},
  author={Ye, Lu and Tao, Ze and Huang, Yong and Li, Yang},
  journal={arXiv preprint arXiv:2402.15220},
  year={2024}
}

@misc{kvcachesize,
  title={LLM Inference Series: 4. KV caching, a deeper look},
  howpublished={\url{https://medium.com/@plienhar/llm-inference-series-4-kv-caching-a-deeper-look-4ba9a77746c8}},
  year={2024},
}

@misc{vllmcode,
  title={vLLM, Easy, fast, and cheap LLM serving for everyone},
  howpublished={\url{https://github.com/vllm-project/vllm?tab=readme-ov-file}},
  year={2024},
}

@misc{sglangcode,
  title={The sglang source code},
  howpublished={\url{https://github.com/sgl-project/sglang}},
  year={2024},
}

@misc{promptbase,
  title={PromptBase: Prompt Marketplace},
  howpublished={\url{https://promptbase.com/}},
  year={2024},
}
@misc{chatgpt,
  title={ChatGPT},
  howpublished={\url{https://chat.openai.com/}},
  year={2024},
}

@misc{beth,
  title={Beth},
  howpublished={\url{https://www.ablera.com/beth-intelligent-virtual-assistant/}},
  year={2024},
}

@misc{copilot,
  title={Copilot},
  howpublished={\url{https://copilot.microsoft.com/}},
  year={2024},
}

@misc{llama13b,
  title={Llama-2-13b},
  howpublished={\url{https://huggingface.co/meta-llama/Llama-2-13b}},
  year={2024},
}

@misc{llama38b,
  title={Llama-3-8b},
  howpublished={\url{https://huggingface.co/meta-llama/Meta-Llama-3-8B}},
  year={2024},
}

@misc{llama7b,
  title={Llama-2-7b},
  howpublished={\url{https://huggingface.co/meta-llama/Llama-2-7b}},
  year={2024},
}

@misc{gpt2,
  title={gpt2},
  howpublished={\url{https://huggingface.co/openai-community/gpt2}},
  year={2024},
}

@misc{awesome,
  title={awesome-chatgpt-prompts},
  howpublished={\url{https://huggingface.co/datasets/fka/awesome-chatgpt-prompts}},
  year={2024},
}

@misc{alpaca,
  title={alpaca-gpt4},
  howpublished={\url{https://huggingface.co/datasets/vicgalle/alpaca-gpt4}},
  year={2024},
}

@article{sha2024prompt,
  title={Prompt Stealing Attacks Against Large Language Models},
  author={Sha, Zeyang and Zhang, Yang},
  journal={arXiv preprint arXiv:2402.12959},
  year={2024}
}

@article{yang2024prsa,
  title={PRSA: Prompt Reverse Stealing Attacks against Large Language Models},
  author={Yang, Yong and Zhang, Xuhong and Jiang, Yi and Chen, Xi and Wang, Haoyu and Ji, Shouling and Wang, Zonghui},
  journal={arXiv preprint arXiv:2402.19200},
  year={2024}
}

@article{perez2022ignore,
  title={Ignore previous prompt: Attack techniques for language models},
  author={Perez, F{\'a}bio and Ribeiro, Ian},
  journal={arXiv preprint arXiv:2211.09527},
  year={2022}
}

@article{zhang2023prompts,
  title={Prompts should not be seen as secrets: Systematically measuring prompt extraction attack success},
  author={Zhang, Yiming and Ippolito, Daphne},
  journal={arXiv preprint arXiv:2307.06865},
  year={2023}
}

@misc{rtpllm,
  title={RTP-LLM},
  howpublished={\url{https://github.com/alibaba/rtp-llm}},
  year={2024},
}

@misc{openaibug,
  title={March 20 ChatGPT outage: Here’s what happened},
  howpublished={\url{https://openai.com/blog/march-20-chatgpt-outage}},
  year={2023},
}

@inproceedings{varadarajan2015placement,
  title={A placement vulnerability study in $\{$Multi-Tenant$\}$ public clouds},
  author={Varadarajan, Venkatanathan and Zhang, Yinqian and Ristenpart, Thomas and Swift, Michael},
  booktitle={24th USENIX Security Symposium (USENIX Security 15)},
  year={2015}
}

@article{sheng2023slora,
  title={S-lora: Serving thousands of concurrent lora adapters},
  author={Sheng, Ying and Cao, Shiyi and Li, Dacheng and Hooper, Coleman and Lee, Nicholas and Yang, Shuo and Chou, Christopher and Zhu, Banghua and Zheng, Lianmin and Keutzer, Kurt and others},
  journal={arXiv preprint arXiv:2311.03285},
  year={2023}
}

@article{shen2023anything,
  title={" do anything now": Characterizing and evaluating in-the-wild jailbreak prompts on large language models},
  author={Shen, Xinyue and Chen, Zeyuan and Backes, Michael and Shen, Yun and Zhang, Yang},
  journal={arXiv preprint arXiv:2308.03825},
  year={2023}
}

@article{rajaraman2024toward,
  title={Toward a Theory of Tokenization in LLMs},
  author={Rajaraman, Nived and Jiao, Jiantao and Ramchandran, Kannan},
  journal={arXiv preprint arXiv:2404.08335},
  year={2024}
}

@article{qin2024mooncake,
  title={Mooncake: Kimi's KVCache-centric Architecture for LLM Serving},
  author={Qin, Ruoyu and Li, Zheming and He, Weiran and Zhang, Mingxing and Wu, Yongwei and Zheng, Weimin and Xu, Xinran},
  journal={arXiv preprint arXiv:2407.00079},
  year={2024}
}

@article{gao2024attentionstore,
  title={AttentionStore: Cost-effective Attention Reuse across Multi-turn Conversations in Large Language Model Serving},
  author={Gao, Bin and He, Zhuomin and Sharma, Puru and Kang, Qingxuan and Jevdjic, Djordje and Deng, Junbo and Yang, Xingkun and Yu, Zhou and Zuo, Pengfei},
  journal={arXiv preprint arXiv:2403.19708},
  year={2024}
}

@article{chen2023punica,
  title={Punica: Multi-tenant lora serving},
  author={Chen, Lequn and Ye, Zihao and Wu, Yongji and Zhuo, Danyang and Ceze, Luis and Krishnamurthy, Arvind},
  journal={arXiv preprint arXiv:2310.18547},
  year={2023}
}

@inproceedings{bang2023gptcache,
  title={GPTCache: An open-source semantic cache for LLM applications enabling faster answers and cost savings},
  author={Bang, Fu},
  booktitle={Proceedings of the 3rd Workshop for Natural Language Processing Open Source Software (NLP-OSS 2023)},
  year={2023}
}

% security issues in multi-tenant environments [begin]
@inproceedings{zhang2012cross,
  title={Cross-VM side channels and their use to extract private keys},
  author={Zhang, Yinqian and Juels, Ari and Reiter, Michael K and Ristenpart, Thomas},
  booktitle={Proceedings of the 2012 ACM conference on Computer and communications security},
  year={2012}
}
@inproceedings{zhang2014cross,
  title={Cross-tenant side-channel attacks in PaaS clouds},
  author={Zhang, Yinqian and Juels, Ari and Reiter, Michael K and Ristenpart, Thomas},
  booktitle={Proceedings of the 2014 ACM SIGSAC Conference on Computer and Communications Security},
  year={2014}
}
@inproceedings{varadarajan2015placement_vul,
  title={A placement vulnerability study in $\{$Multi-Tenant$\}$ public clouds},
  author={Varadarajan, Venkatanathan and Zhang, Yinqian and Ristenpart, Thomas and Swift, Michael},
  booktitle={24th USENIX Security Symposium (USENIX Security 15)},
  year={2015}
}
@inproceedings{xiao2016one,
  title={One bit flips, one cloud flops:$\{$Cross-VM$\}$ row hammer attacks and privilege escalation},
  author={Xiao, Yuan and Zhang, Xiaokuan and Zhang, Yinqian and Teodorescu, Radu},
  booktitle={25th USENIX security symposium (USENIX Security 16)},
  year={2016}
}
@inproceedings{zhang2018level,
  title={Os-level side channels without procfs: Exploring cross-app information leakage on ios},
  author={Zhang, Xiaokuan and Wang, Xueqiang and Bai, Xiaolong and Zhang, Yinqian and Wang, XiaoFeng},
  booktitle={Proceedings of the Symposium on Network and Distributed System Security},
  year={2018}
}
@inproceedings{wang2023danger,
  title={The Danger of Minimum Exposures: Understanding Cross-App Information Leaks on iOS through Multi-Side-Channel Learning},
  author={Wang, Zihao and Guan, Jiale and Wang, XiaoFeng and Wang, Wenhao and Xing, Luyi and Alharbi, Fares},
  booktitle={Proceedings of the 2023 ACM SIGSAC Conference on Computer and Communications Security},
  year={2023}
}
@inproceedings{chen2014peeking,
  title={Peeking into your app without actually seeing it:$\{$UI$\}$ state inference and novel android attacks},
  author={Chen, Qi Alfred and Qian, Zhiyun and Mao, Z Morley},
  booktitle={23rd USENIX Security Symposium (USENIX Security 14)},
  year={2014}
}
@inproceedings{jana2012memento,
  title={Memento: Learning secrets from process footprints},
  author={Jana, Suman and Shmatikov, Vitaly},
  booktitle={2012 IEEE Symposium on Security and Privacy},
  year={2012},
  organization={IEEE}
}
@inproceedings{zhang2009peeping,
  title={Peeping Tom in the Neighborhood: Keystroke Eavesdropping on Multi-User Systems.},
  author={Zhang, Kehuan and Wang, XiaoFeng},
  booktitle={USENIX Security Symposium},
  year={2009}
}
@inproceedings{zhang2016return,
  title={Return-oriented flush-reload side channels on arm and their implications for android devices},
  author={Zhang, Xiaokuan and Xiao, Yuan and Zhang, Yinqian},
  booktitle={Proceedings of the 2016 ACM SIGSAC Conference on Computer and Communications Security},
  year={2016}
}
@inproceedings{narayan2021swivel,
  title={Swivel: Hardening $\{$WebAssembly$\}$ against spectre},
  author={Narayan, Shravan and Disselkoen, Craig and Moghimi, Daniel and Cauligi, Sunjay and Johnson, Evan and Gang, Zhao and Vahldiek-Oberwagner, Anjo and Sahita, Ravi and Shacham, Hovav and Tullsen, Dean and others},
  booktitle={30th USENIX Security Symposium (USENIX Security 21)},
  year={2021}
}
@INPROCEEDINGS{kocher2019spectre,
  author={Kocher, Paul and Horn, Jann and Fogh, Anders and Genkin, Daniel and Gruss, Daniel and Haas, Werner and Hamburg, Mike and Lipp, Moritz and Mangard, Stefan and Prescher, Thomas and Schwarz, Michael and Yarom, Yuval},
  booktitle={2019 IEEE Symposium on Security and Privacy (SP)},
  title={Spectre Attacks: Exploiting Speculative Execution},
  year={2019},
  doi={10.1109/SP.2019.00002}}

@misc{openAIstream,
  title={OpenAI API reference},
  howpublished={\url{https://platform.openai.com/docs/api-reference}},
  year={2024},
}

@misc{batchingpolicies,
  title={How continuous batching enables 23x throughput in LLM inference while reducing p50 latency},
  howpublished={\url{https://www.anyscale.com/blog/continuous-batching-llm-inference}},
  year={2024},
}

@article{white2023prompt,
  title={A prompt pattern catalog to enhance prompt engineering with chatgpt},
  author={White, Jules and Fu, Quchen and Hays, Sam and Sandborn, Michael and Olea, Carlos and Gilbert, Henry and Elnashar, Ashraf and Spencer-Smith, Jesse and Schmidt, Douglas C},
  journal={arXiv preprint arXiv:2302.11382},
  year={2023}
}

@article{wang2023prompt,
  title={Prompt engineering for healthcare: Methodologies and applications},
  author={Wang, Jiaqi and Shi, Enze and Yu, Sigang and Wu, Zihao and Ma, Chong and Dai, Haixing and Yang, Qiushi and Kang, Yanqing and Wu, Jinru and Hu, Huawen and others},
  journal={arXiv preprint arXiv:2304.14670},
  year={2023}
}

@misc{prompttemplates,
  title={Prompt Engineering: Enhancing Language AI for Optimal Performance},
  howpublished={\url{https://medium.com/@PrabodhaOnline/prompt-engineering-enhancing-language-ai-for-optimal-performance-f33721396e0}},
  year={2024},
}

@misc{llama70b,
  title={Llama 2 70B Chat - GPTQ},
  howpublished={\url{https://huggingface.co/TheBloke/Llama-2-70B-Chat-GPTQ
}},
  year={2024},
}

@article{zhao2024llm,
  title={Llm app store analysis: A vision and roadmap},
  author={Zhao, Yanjie and Hou, Xinyi and Wang, Shenao and Wang, Haoyu},
  journal={arXiv preprint arXiv:2404.12737},
  year={2024}
}

@misc{batchsize,
  title={LLM Inference Performance Engineering: Best Practices},
  howpublished={\url{https://www.databricks.com/blog/llm-inference-performance-engineering-best-practices
}},
  year={2024},
}

@misc{gpt4limit,
  title={GPT4 requests limit},
  howpublished={\url{https://community.openai.com/t/whys-gpt-4o-insanely-limited-to-free-users-and-even-plus-users-it-literally-barely-gives-you-5-messages-in-5-6-hours-to-the-free-users/769852
}},
  year={2024},
}
@article{ding2023enhancing,
  title={Enhancing Chat Language Models by Scaling High-quality Instructional Conversations},
  author={Ding, Ning and Chen, Yulin and Xu, Bokai and Qin, Yujia and Zheng, Zhi and Hu, Shengding and Liu, Zhiyuan and Sun, Maosong and Zhou, Bowen},
  journal={arXiv preprint arXiv:2305.14233},
  year={2023}
}
% security issues in multi-tenant environments [end]

@misc{openaitokenizer,
  title={OpenAI tokenizer},
  howpublished={\url{https://platform.openai.com/tokenizer
}},
  year={2024},
}

@misc{tiktokenizer,
  title={Tiktokenizer},
  howpublished={\url{https://tiktokenizer.vercel.app/
}},
  year={2024},
}

@misc{openLLMleaderboard,
  title={Open LLM Leaderboard},
  howpublished={\url{https://huggingface.co/open-llm-leaderboard
}},
  year={2024},
}

@article{ainslie2023gqa,
  title={Gqa: Training generalized multi-query transformer models from multi-head checkpoints},
  author={Ainslie, Joshua and Lee-Thorp, James and de Jong, Michiel and Zemlyanskiy, Yury and Lebr{\'o}n, Federico and Sanghai, Sumit},
  journal={arXiv preprint arXiv:2305.13245},
  year={2023}
}

@inproceedings{liu2023side,
  title={$\{$Side-Channel$\}$ Attacks on Optane Persistent Memory},
  author={Liu, Sihang and Kanniwadi, Suraaj and Schwarzl, Martin and Kogler, Andreas and Gruss, Daniel and Khan, Samira},
  booktitle={32nd USENIX Security Symposium (USENIX Security 23)},
  pages={6807--6824},
  year={2023}
}

@misc{deeperlookKV,
  title={LLM Inference Series: 4. KV caching, a deeper look},
  howpublished={\url{https://medium.com/@plienhar/llm-inference-series-4-kv-caching-a-deeper-look-4ba9a77746c8
}},
  year={2024},
}

@inproceedings{wu2025prompt,
  author    = {Wu, Guanlong and Zhang, Zheng and Zhang, Yao and Wang, Weili and Niu, Jianyu and Wu, Ye and Zhang, Yinqian},
  title     = {I Know What You Asked: Prompt Leakage via KV-Cache Sharing in Multi-Tenant LLM Serving},
  booktitle = {Proceedings of the 2025 Network and Distributed System Security (NDSS) Symposium},
  year      = {2025},
  address   = {San Diego, CA, USA},
  doi       = {10.14722/ndss.2025.241772},
  isbn      = {979-8-9894372-8-3}
}