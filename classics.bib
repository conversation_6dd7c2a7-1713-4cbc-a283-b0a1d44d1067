
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee sgx

@incollection{mckeen2016intel,
  title={Intel{\textregistered} software guard extensions (intel{\textregistered} sgx) support for dynamic memory management inside an enclave},
  author={<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>tai and C<PERSON>pi, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON>},
  booktitle={Proceedings of the Hardware and Architectural Support for Security and Privacy 2016},
  pages={1--9},
  year={2016}
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee tdx

@article{cheng2023intel,
  title={Intel TDX Demystified: A Top-Down Approach},
  author={<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>hong<PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>},
  journal={arXiv preprint arXiv:2303.15540},
  year={2023}
}



@inproceedings{sahita2021security,
  title={Security analysis of confidential-compute instruction set architecture for virtualized workloads},
  author={Sahita, Ravi and Caspi, Dror and Huntley, Barry and Scarlata, Vincent and Chaikin, Baruch and Chhabra, Siddhartha and Aharon, Arie and Ouziel, Ido},
  booktitle={2021 International Symposium on Secure and Private Execution Environment Design (SEED)},
  pages={121--131},
  year={2021},
  organization={IEEE}
}


@article{sardar2023sok,
  title={SoK: Attestation in confidential computing},
  author={Sardar, M and Fossati, Thomas and Frost, Simon},
  journal={ResearchGate pre-print},
  year={2023}
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee A-TLS

@article{knauth2018integrating,
  title={Integrating remote attestation with transport layer security},
  author={Knauth, Thomas and Steiner, Michael and Chakrabarti, Somnath and Lei, Li and Xing, Cedric and Vij, Mona},
  journal={arXiv preprint arXiv:1801.05863},
  year={2018}
}




%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee trustzone
@misc{arm2004trustzone,
  title={TrustZone Technology},
  author={ARM, ARM},
  year={2004}
}




%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee cca
@inproceedings{zhang2023shelter,
  title={SHELTER: Extending Arm CCA with Isolation in User Space},
  author={Zhang, Yiming and Hu, Yuxin and Ning, Zhenyu and Zhang, Fengwei and Luo, Xiapu and Huang, Haoyang and Yan, Shoumeng and He, Zhengyu},
  booktitle={32nd USENIX Security Symposium (USENIX Security’23)},
  year={2023}
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee sev

@article{kaplan2016amd,
  title={AMD memory encryption},
  author={Kaplan, David and Powell, Jeremy and Woller, Tom},
  journal={White paper},
  pages={13},
  year={2016}
}




%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee sev-snp


@article{sev2020strengthening,
  title={Strengthening VM isolation with integrity protection and more},
  author={Sev-Snp, AMD},
  journal={White Paper, January},
  volume={53},
  pages={1450--1465},
  year={2020}
}



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee runtime



@inproceedings{lee2020keystone,
  title={Keystone: An open framework for architecting trusted execution environments},
  author={Lee, Dayeol and Kohlbrenner, David and Shinde, Shweta and Asanovi{\'c}, Krste and Song, Dawn},
  booktitle={Proceedings of the Fifteenth European Conference on Computer Systems},
  pages={1--16},
  year={2020}
}

@inproceedings{shen2020occlum,
  title={Occlum: Secure and efficient multitasking inside a single enclave of intel sgx},
  author={Shen, Youren and Tian, Hongliang and Chen, Yu and Chen, Kang and Wang, Runji and Xu, Yi and Xia, Yubin and Yan, Shoumeng},
  booktitle={Proceedings of the Twenty-Fifth International Conference on Architectural Support for Programming Languages and Operating Systems},
  pages={955--970},
  year={2020}
}



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee db

@inproceedings{priebe2018enclavedb,
  title={EnclaveDB: A secure database using SGX},
  author={Priebe, Christian and Vaswani, Kapil and Costa, Manuel},
  booktitle={2018 IEEE Symposium on Security and Privacy (SP)},
  pages={264--278},
  year={2018},
  organization={IEEE}
}


@article{vinayagamurthy2019stealthdb,
  title={StealthDB: a Scalable Encrypted Database with Full SQL Query Support.},
  author={Vinayagamurthy, Dhinakaran and Gribov, Alexey and Gorbunov, Sergey},
  journal={Proc. Priv. Enhancing Technol.},
  volume={2019},
  number={3},
  pages={370--388},
  year={2019}
}

@inproceedings{li2023encrypted,
  title={Encrypted Databases Made Secure Yet Maintainable},
  author={Li, Mingyu and Zhao, Xuyang and Chen, Le and Tan, Cheng and Li, Huorong and Wang, Sheng and Mi, Zeyu and Xia, Yubin and Li, Feifei and Chen, Haibo},
  booktitle={17th USENIX Symposium on Operating Systems Design and Implementation (OSDI 23)},
  pages={117--133},
  year={2023}
}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee gpu


@inproceedings{volos2018graviton,
  title={Graviton: Trusted execution environments on $\{$GPUs$\}$},
  author={Volos, Stavros and Vaswani, Kapil and Bruno, Rodrigo},
  booktitle={13th USENIX Symposium on Operating Systems Design and Implementation (OSDI 18)},
  pages={681--696},
  year={2018}
}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee tpm

@article{wang2023svtpm,
  title={SvTPM: SGX-based Virtual Trusted Platform Modules for Cloud Computing},
  author={Wang, Juan and Wang, Jie and Fan, Chengyang and Yan, Fei and Cheng, Yueqiang and Zhang, Yinqian and Zhang, Wenhui and Yang, Mengda and Hu, Hongxin},
  journal={IEEE Transactions on Cloud Computing},
  year={2023},
  publisher={IEEE}
}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee network

@article{wang2020s,
  title={S-blocks: Lightweight and trusted virtual security function with SGX},
  author={Wang, Juan and Hao, Shirong and Hu, Hongxin and Zhao, Bo and Li, Hongda and Zhang, Wenhui and Xu, Jun and Liu, Peng and Ma, Jing},
  journal={IEEE Transactions on Cloud Computing},
  volume={10},
  number={2},
  pages={1082--1099},
  year={2020},
  publisher={IEEE}
}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tee mktme


@article{sardar2021demystifying,
  title={Demystifying attestation in intel trust domain extensions via formal verification},
  author={Sardar, Muhammad Usama and Musaev, Saidgani and Fetzer, Christof},
  journal={IEEE access},
  volume={9},
  pages={83067--83079},
  year={2021},
  publisher={IEEE}
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%  sgx2 comparison with sgx1
@inproceedings{sgx2_benchmark,
author = {El-Hindi, Muhammad and Ziegler, Tobias and Heinrich, Matthias and Lutsch, Adrian and Zhao, Zheguang and Binnig, Carsten},
title = {Benchmarking the Second Generation of Intel SGX Hardware},
year = {2022},
isbn = {9781450393782},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/3533737.3535098},
doi = {10.1145/3533737.3535098},
abstract = {In recent years, trusted execution environments (TEEs) such as Intel Software Guard Extensions (SGX) have gained a lot of attention in the database community. This is because TEEs provide an interesting platform for building trusted databases in the cloud. However, until recently SGX was only available on low-end single socket servers built on the Intel Xeon E3 processor generation and came with many restrictions for building DBMSs. With the availability of the new Ice Lake processors, Intel provides a new implementation of the SGX technology that supports high-end multi-socket servers. With this new implementation, which we refer to as SGXv2 in this paper, Intel promises to address several limitations of SGX enclaves. This raises the question whether previous efforts to overcome the limitations of SGX for DBMSs are still applicable and if the new generation of SGX can truly deliver on the promise to secure data without compromising on performance. To answer this question, in this paper we conduct a first systematic performance study of Intel SGXv2 and compare it to the previous generation of SGX.},
booktitle = {Proceedings of the 18th International Workshop on Data Management on New Hardware},
articleno = {5},
numpages = {8},
location = {Philadelphia, PA, USA},
series = {DaMoN '22}
}


@inproceedings{tsai2017graphene,
  title={$\{$Graphene-SGX$\}$: A practical library $\{$OS$\}$ for unmodified applications on $\{$SGX$\}$},
  author={Tsai, Chia-Che and Porter, Donald E and Vij, Mona},
  booktitle={2017 USENIX Annual Technical Conference (USENIX ATC 17)},
  pages={645--658},
  year={2017}
}




@inproceedings{d2018wearable,
  title={Wearable IoT Security and Privacy: A Review from Technology and Policy Perspective},
  author={D’Mello, Onyeka and Gelin, Mathilde and Khelil, Fatma Ben and Surek, Rojen Erik and Chi, Huihui},
  booktitle={International Conference on Future Network Systems and Security},
  pages={162--177},
  year={2018},
  organization={Springer}
}

@inproceedings{chen2009shadownet,
  title={ShadowNet: a platform for rapid and safe network evolution},
  author={Chen, Xu and Mao, Z Morley and Van der Merwe, Jacobus},
  booktitle={Proceedings of the 2009 conference on USENIX Annual technical conference},
  pages={3--3},
  year={2009}
}


@inproceedings{bouziani2019comparative,
  title={A Comparative study of Open Source IDSs according to their Ability to Detect Attacks},
  author={Bouziani, Ossama and Benaboud, Hafssa and Chamkar, Achraf Samir and Lazaar, Saiida},
  booktitle={Proceedings of the 2nd International Conference on Networking, Information Systems \& Security},
  pages={1--5},
  year={2019}
}
































@misc{standardieee,
  title={IEEE Std 1003.1-2017 (revision of IEEE Std 1003.1-2008)-IEEE standard for information technology--portable operating system interface (POSIX{\textregistered}) base specifications, issue 7},
  author={Standard, POSIX}
}





@article{mueller2012stuxnet,
  title={The stuxnet worm},
  author={Mueller, Paul and Yadegari, Babak},
  journal={D{\'e}partement des sciences de linformatique, Universit{\'e} de lArizona. Recuperado de: https://www2. cs. arizona. edu/\~{} collberg/Teaching/466-566/2012/Resources/presentations/topic9-final/report. pdf},
  year={2012}
}

%%%%%%%%%%%%%%%%%%%%%%theory without perf

@inproceedings{jaeger2006prima,
  title={PRIMA: policy-reduced integrity measurement architecture},
  author={Jaeger, Trent and Sailer, Reiner and Shankar, Umesh},
  booktitle={Proceedings of the eleventh ACM symposium on Access control models and technologies},
  pages={19--28},
  year={2006},
  organization={ACM}
}



@article{denning1976lattice,
  title={A lattice model of secure information flow},
  author={Denning, Dorothy E},
  journal={Communications of the ACM},
  volume={19},
  number={5},
  pages={236--243},
  year={1976},
  publisher={ACM}
}



@techreport{biba1977integrity,
  title={Integrity considerations for secure computer systems},
  author={Biba, Kenneth J},
  year={1977},
  institution={MITRE CORP BEDFORD MA}
}


@inproceedings{clark1987comparison,
  title={A comparison of commercial and military computer security policies},
  author={Clark, David D and Wilson, David R},
  booktitle={1987 IEEE Symposium on Security and Privacy},
  pages={184--184},
  year={1987},
  organization={IEEE}
}


@inproceedings{goguen1982security,
  title={Security policies and security models},
  author={Goguen, Joseph A and Meseguer, Jos{\'e}},
  booktitle={1982 IEEE Symposium on Security and Privacy},
  pages={11--11},
  year={1982},
  organization={IEEE}
}


@inproceedings{shankar2006toward,
  title={Toward Automated Information-Flow Integrity Verification for Security-Critical Applications.},
  author={Shankar, Umesh and Jaeger, Trent and Sailer, Reiner},
  booktitle={NDSS},
  year={2006}
}


@inproceedings{fraser1999lomac,
  title={LOMAC-low water-mark mandatory access control for Linux},
  author={Fraser, Tim},
  booktitle={The 8th USENIX Security Symposium Washington DC, August, 1999},
  year={1999}
}


@techreport{bell1976secure,
  title={Secure computer system: Unified exposition and multics interpretation},
  author={Bell, D Elliott and La Padula, Leonard J},
  year={1976},
  institution={MITRE CORP BEDFORD MA}
}






@inproceedings{nath2019poltree,
  title={PolTree: A Data Structure for Making Efficient Access Decisions in ABAC},
  author={Nath, Ronit and Das, Saptarshi and Sural, Shamik and Vaidya, Jaideep and Atluri, Vijay},
  booktitle={Proceedings of the 24th ACM Symposium on Access Control Models and Technologies},
  pages={25--35},
  year={2019},
  organization={ACM}
}



@inproceedings{pasquier2017practical,
  title={Practical whole-system provenance capture},
  author={Pasquier, Thomas and Han, Xueyuan and Goldstein, Mark and Moyer, Thomas and Eyers, David and Seltzer, Margo and Bacon, Jean},
  booktitle={Proceedings of the 2017 Symposium on Cloud Computing},
  pages={405--418},
  year={2017},
  organization={ACM}
}



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%perf data 
@inproceedings{morris2002linux,
  title={Linux security modules: General security support for the linux kernel},
  author={Morris, James and Smalley, Stephen and Kroah-Hartman, Greg},
  booktitle={USENIX Security Symposium},
  pages={17--31},
  year={2002},
  organization={ACM Berkeley, CA}
}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%hook placement %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
@inproceedings{ganapathy2007mining,
  title={Mining security-sensitive operations in legacy code using concept analysis},
  author={Ganapathy, Vinod and King, David and Jaeger, Trent and Jha, Somesh},
  booktitle={29th International Conference on Software Engineering (ICSE'07)},
  pages={458--467},
  year={2007},
  organization={IEEE}
}



@techreport{kilpatrick2003securing,
  title={Securing the X Window system with SELinux},
  author={Kilpatrick, Doug and Salamon, Wayne and Vance, Chris},
  year={2003},
  institution={Technical Report 03-006, NAI Labs}
}



@inproceedings{wright2002linux,
  title={Linux security module framework},
  author={Wright, Chris and Cowan, Crispin and Morris, James and Smalley, Stephen and Kroah-Hartman, Greg},
  booktitle={Ottawa Linux Symposium},
  volume={8032},
  pages={6--16},
  year={2002}
}



@inproceedings{muthukumaran2012leveraging,
  title={Leveraging 'choice' in authorization hook placement},
  author={Muthukumaran, D and Jaeger, T and Ganapathy, V},
  booktitle={19th ACM Conference on Computer and Commumications Security},
  year={2012}
}


@inproceedings{muthukumaran2015producing,
  title={Producing hook placements to enforce expected access control policies},
  author={Muthukumaran, Divya and Talele, Nirupama and Jaeger, Trent and Tan, Gang},
  booktitle={International Symposium on Engineering Secure Software and Systems},
  pages={178--195},
  year={2015},
  organization={Springer}
}



@inproceedings{ganapathy2005automatic,
  title={Automatic placement of authorization hooks in the Linux security modules framework},
  author={Ganapathy, Vinod and Jaeger, Trent and Jha, Somesh},
  booktitle={Proceedings of the 12th ACM conference on Computer and communications security},
  pages={330--339},
  year={2005}
}


@inproceedings{ganapathy2006towards,
  title={Towards automated authorization policy enforcement},
  author={Ganapathy, Vinod and Jaeger, Trent},
  booktitle={Proceedings of Second Annual Security Enhanced Linux Symposium},
  year={2006}
}



@inproceedings{edwards2002runtime,
  title={Runtime verification of authorization hook placement for the Linux security modules framework},
  author={Edwards, Antony and Jaeger, Trent and Zhang, Xiaolan},
  booktitle={Proceedings of the 9th ACM Conference on Computer and Communications Security},
  pages={225--234},
  year={2002}
}


@inproceedings{tan2008autoises,
  title={AutoISES: Automatically Inferring Security Specification and Detecting Violations.},
  author={Tan, Lin and Zhang, Xiaolan and Ma, Xiao and Xiong, Weiwei and Zhou, Yuanyuan},
  booktitle={USENIX Security Symposium},
  pages={379--394},
  year={2008}
}


@inproceedings{zhang2002using,
  title={Using CQUAL for Static Analysis of Authorization Hook Placement.},
  author={Zhang, Xiaolan and Edwards, Antony and Jaeger, Trent},
  booktitle={USENIX Security Symposium},
  pages={33--48},
  year={2002}
}



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%fs bench %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
@inproceedings{chen2007keeping,
  title={Keeping kernel performance from regressions},
  author={Chen, Tim and Ananiev, Leonid I and Tikhonov, Alexander V},
  booktitle={Linux Symposium},
  volume={1},
  pages={93--102},
  year={2007}
}



@inproceedings{ren2019analysis,
  title={An analysis of performance evolution of Linux's core operations},
  author={Ren, Xiang and Rodrigues, Kirk and Chen, Luyuan and Vega, Camilo and Stumm, Michael and Yuan, Ding},
  booktitle={Proceedings of the 27th ACM Symposium on Operating Systems Principles},
  pages={554--569},
  year={2019}
}


%%%%%%%%%%%%%%%%%%%%%%%%perf%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


@inproceedings{loscocco2001integrating,
  title={Integrating Flexible Support for Security Policies into the Linux Operating System.},
  author={Loscocco, Peter and Smalley, Stephen},
  booktitle={USENIX Annual Technical Conference, FREENIX Track},
  pages={29--42},
  year={2001}
}



%%%%%%%%%%%%%%%%%%%%%%%%perf%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%




































%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%graph cut %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

@article{ahuja1995network,
  title={Network flows: theory, algorithms and applications},
  author={Ahuja, Ravindra K and Magnanti, Thomas L and Orlin, James B and Weihe, K},
  journal={ZOR-Methods and Models of Operations Research},
  volume={41},
  number={3},
  pages={252--254},
  year={1995},
  publisher={Wurzburg, Physica-Verlag, 1972-1995.}
}



@article{ahujia1993network,
  title={Network flows: Theory, algorithms and applications},
  author={Ahujia, RK and Magnanti, Thomas L and Orlin, James B},
  journal={New Jersey: Rentice-Hall},
  year={1993}
}



@article{goldberg1988new,
  title={A new approach to the maximum-flow problem},
  author={Goldberg, Andrew V and Tarjan, Robert E},
  journal={Journal of the ACM (JACM)},
  volume={35},
  number={4},
  pages={921--940},
  year={1988},
  publisher={ACM New York, NY, USA}
}



@article{boykov2004experimental,
  title={An experimental comparison of min-cut/max-flow algorithms for energy minimization in vision},
  author={Boykov, Yuri and Kolmogorov, Vladimir},
  journal={IEEE transactions on pattern analysis and machine intelligence},
  volume={26},
  number={9},
  pages={1124--1137},
  year={2004},
  publisher={IEEE}
}


@article{goldberg2014efficient,
  title={Efficient maximum flow algorithms},
  author={Goldberg, Andrew V and Tarjan, Robert E},
  journal={Communications of the ACM},
  volume={57},
  number={8},
  pages={82--89},
  year={2014},
  publisher={ACM New York, NY, USA}
}


@incollection{dinitz2006dinitz,
  title={Dinitz’algorithm: The original version and Even’s version},
  author={Dinitz, Yefim},
  booktitle={Theoretical computer science},
  pages={218--240},
  year={2006},
  publisher={Springer}
}


@article{dinitz2017hybrid,
  title={Hybrid Bellman--Ford--Dijkstra algorithm},
  author={Dinitz, Yefim and Itzhak, Rotem},
  journal={Journal of Discrete Algorithms},
  volume={42},
  pages={35--44},
  year={2017},
  publisher={Elsevier}
}


@article{edmonds1972theoretical,
  title={Theoretical improvements in algorithmic efficiency for network flow problems},
  author={Edmonds, Jack and Karp, Richard M},
  journal={Journal of the ACM (JACM)},
  volume={19},
  number={2},
  pages={248--264},
  year={1972},
  publisher={ACM New York, NY, USA}
}



@incollection{ford2009maximal,
  title={Maximal flow through a network},
  author={Ford, Lester Randolph and Fulkerson, Delbert R},
  booktitle={Classic papers in combinatorics},
  pages={243--248},
  year={2009},
  publisher={Springer}
}


@article{ford1957simple,
  title={A simple algorithm for finding maximal network flows and an application to the Hitchcock problem},
  author={Ford, Lester Randolph and Fulkerson, Delbert R},
  journal={Canadian journal of Mathematics},
  volume={9},
  pages={210--218},
  year={1957},
  publisher={Cambridge University Press}
}



@article{fordandfulkerson,
  title={andFulkerson, DR: FlowsinNetworks},
  author={Ford Jr, LR},
  journal={PrincetonUniversityPress. Princeton},
  number={1962}
}





%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%posix%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

@inproceedings{gardner2014local,
  title={Local reasoning for the POSIX file system},
  author={Gardner, Philippa and Ntzik, Gian and Wright, Adam},
  booktitle={European Symposium on Programming Languages and Systems},
  pages={169--188},
  year={2014},
  organization={Springer}
}





@article{walli1995posix,
  title={The POSIX family of standards},
  author={Walli, Stephen R},
  journal={StandardView},
  volume={3},
  number={1},
  pages={11--17},
  year={1995},
  publisher={ACM New York, NY, USA}
}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%



%%%%%%%%%%%%%%%%confused 
@article{hardy1988confused,
  title={The Confused Deputy: (or why capabilities might have been invented)},
  author={Hardy, Norm},
  journal={ACM SIGOPS Operating Systems Review},
  volume={22},
  number={4},
  pages={36--38},
  year={1988},
  publisher={ACM New York, NY, USA}
}








@article{denning1976lattice,
  title={A lattice model of secure information flow},
  author={Denning, Dorothy E},
  journal={Communications of the ACM},
  volume={19},
  number={5},
  pages={236--243},
  year={1976},
  publisher={ACM}
}



@inproceedings{clark1987comparison,
  title={A comparison of commercial and military computer security policies},
  author={Clark, David D and Wilson, David R},
  booktitle={1987 IEEE Symposium on Security and Privacy},
  pages={184--184},
  year={1987},
  organization={IEEE}
}


@inproceedings{goguen1982security,
  title={Security policies and security models},
  author={Goguen, Joseph A and Meseguer, Jos{\'e}},
  booktitle={1982 IEEE Symposium on Security and Privacy},
  pages={11--11},
  year={1982},
  organization={IEEE}
}


@article{nakamura2015reducing,
  title={Reducing Resource Consumption of SELinux for Embedded Systems with Contributions to Open-Source Ecosystems},
  author={Nakamura, Yuichi and Sameshima, Yoshiki and Yamauchi, Toshihiro},
  journal={Journal of Information Processing},
  volume={23},
  number={5},
  pages={664--672},
  year={2015},
  publisher={Information Processing Society of Japan}
}

@misc{lsm:modulestackingforall, 
      title={LSM: Module stacking for all},
      url={https://lwn.net/Articles/786307/},
      author={Casey Schaufler},
      year={2019}
}

@misc{integrity:linuxintegritymodule(lim), 
      title={Integrity: Linux Integrity Module(LIM)},
      url={https://lwn.net/Articles/287790/},
      author={Mimi Zohar},
      year={2008}
}


@inproceedings{shankar2006cwlite,
  title={Toward Automated Information-Flow Integrity Verification for Security-Critical Applications.},
  author={Shankar, Umesh and Jaeger, Trent and Sailer, Reiner},
  booktitle={NDSS},
  year={2006}
}




@inproceedings{pasquier2017practical,
  title={Practical whole-system provenance capture},
  author={Pasquier, Thomas and Han, Xueyuan and Goldstein, Mark and Moyer, Thomas and Eyers, David and Seltzer, Margo and Bacon, Jean},
  booktitle={Proceedings of the 2017 Symposium on Cloud Computing},
  pages={405--418},
  year={2017},
  organization={ACM}
}






@techreport{kilpatrick2003securing,
  title={Securing the X Window system with SELinux},
  author={Kilpatrick, Doug and Salamon, Wayne and Vance, Chris},
  year={2003},
  institution={Technical Report 03-006, NAI Labs}
}









































%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%graph cut %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

@article{ahuja1995network,
  title={Network flows: theory, algorithms and applications},
  author={Ahuja, Ravindra K and Magnanti, Thomas L and Orlin, James B and Weihe, K},
  journal={ZOR-Methods and Models of Operations Research},
  volume={41},
  number={3},
  pages={252--254},
  year={1995},
  publisher={Wurzburg, Physica-Verlag, 1972-1995.}
}



@article{ahujia1993network,
  title={Network flows: Theory, algorithms and applications},
  author={Ahujia, RK and Magnanti, Thomas L and Orlin, James B},
  journal={New Jersey: Rentice-Hall},
  year={1993}
}



@article{goldberg1988new,
  title={A new approach to the maximum-flow problem},
  author={Goldberg, Andrew V and Tarjan, Robert E},
  journal={Journal of the ACM (JACM)},
  volume={35},
  number={4},
  pages={921--940},
  year={1988},
  publisher={ACM New York, NY, USA}
}



@article{boykov2004experimental,
  title={An experimental comparison of min-cut/max-flow algorithms for energy minimization in vision},
  author={Boykov, Yuri and Kolmogorov, Vladimir},
  journal={IEEE transactions on pattern analysis and machine intelligence},
  volume={26},
  number={9},
  pages={1124--1137},
  year={2004},
  publisher={IEEE}
}


@article{goldberg2014efficient,
  title={Efficient maximum flow algorithms},
  author={Goldberg, Andrew V and Tarjan, Robert E},
  journal={Communications of the ACM},
  volume={57},
  number={8},
  pages={82--89},
  year={2014},
  publisher={ACM New York, NY, USA}
}


@incollection{dinitz2006dinitz,
  title={Dinitz’algorithm: The original version and Even’s version},
  author={Dinitz, Yefim},
  booktitle={Theoretical computer science},
  pages={218--240},
  year={2006},
  publisher={Springer}
}


@article{dinitz2017hybrid,
  title={Hybrid Bellman--Ford--Dijkstra algorithm},
  author={Dinitz, Yefim and Itzhak, Rotem},
  journal={Journal of Discrete Algorithms},
  volume={42},
  pages={35--44},
  year={2017},
  publisher={Elsevier}
}


@article{edmonds1972theoretical,
  title={Theoretical improvements in algorithmic efficiency for network flow problems},
  author={Edmonds, Jack and Karp, Richard M},
  journal={Journal of the ACM (JACM)},
  volume={19},
  number={2},
  pages={248--264},
  year={1972},
  publisher={ACM New York, NY, USA}
}



@incollection{ford2009maximal,
  title={Maximal flow through a network},
  author={Ford, Lester Randolph and Fulkerson, Delbert R},
  booktitle={Classic papers in combinatorics},
  pages={243--248},
  year={2009},
  publisher={Springer}
}


@article{ford1957simple,
  title={A simple algorithm for finding maximal network flows and an application to the Hitchcock problem},
  author={Ford, Lester Randolph and Fulkerson, Delbert R},
  journal={Canadian journal of Mathematics},
  volume={9},
  pages={210--218},
  year={1957},
  publisher={Cambridge University Press}
}



@article{fordandfulkerson,
  title={andFulkerson, DR: FlowsinNetworks},
  author={Ford Jr, LR},
  journal={PrincetonUniversityPress. Princeton},
  number={1962}
}





%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%posix%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


@inproceedings{atlidakis2016posix,
  title={POSIX abstractions in modern operating systems: The old, the new, and the missing},
  author={Atlidakis, Vaggelis and Andrus, Jeremy and Geambasu, Roxana and Mitropoulos, Dimitris and Nieh, Jason},
  booktitle={Proceedings of the Eleventh European Conference on Computer Systems},
  pages={1--17},
  year={2016}
}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%compiler vs. os%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


@inproceedings{arden2012sharing,
  title={Sharing mobile code securely with information flow control},
  author={Arden, Owen and George, Michael D and Liu, Jed and Vikram, K and Askarov, Aslan and Myers, Andrew C},
  booktitle={2012 IEEE Symposium on Security and Privacy},
  pages={191--205},
  year={2012},
  organization={IEEE}
}

@inproceedings{chandra2007fine,
  title={Fine-grained information flow analysis and enforcement in a java virtual machine},
  author={Chandra, Deepak and Franz, Michael},
  booktitle={Twenty-Third Annual Computer Security Applications Conference (ACSAC 2007)},
  pages={463--475},
  year={2007},
  organization={IEEE}
}




@inproceedings{liu2009fabric,
  title={Fabric: A platform for secure distributed computation and storage},
  author={Liu, Jed and George, Michael D and Vikram, Krishnaprasad and Qi, Xin and Waye, Lucas and Myers, Andrew C},
  booktitle={Proceedings of the ACM SIGOPS 22nd symposium on Operating systems principles},
  pages={321--334},
  year={2009}
}



@article{myers2001jif,
  title={Jif: Java information flow},
  author={Myers, Andrew C and Zheng, Lantian and Zdancewic, Steve and Chong, Stephen and Nystrom, Nathaniel},
  journal={Software release. Located at http://www. cs. cornell. edu/jif},
  volume={2005},
  year={2001}
}


@inproceedings{simonet2003flow,
  title={Flow Caml in a nutshell},
  author={Simonet, Vincent and Rocquencourt, Inria},
  booktitle={Proceedings of the first APPSEM-II workshop},
  pages={152--165},
  year={2003}
}



@article{efstathopoulos2005labels,
  title={Labels and event processes in the Asbestos operating system},
  author={Efstathopoulos, Petros and Krohn, Maxwell and VanDeBogart, Steve and Frey, Cliff and Ziegler, David and Kohler, Eddie and Mazieres, David and Kaashoek, Frans and Morris, Robert},
  journal={ACM SIGOPS Operating Systems Review},
  volume={39},
  number={5},
  pages={17--30},
  year={2005},
  publisher={ACM New York, NY, USA}
}



@article{vandebogart2007labels,
  title={Labels and event processes in the Asbestos operating system},
  author={Vandebogart, Steve and Efstathopoulos, Petros and Kohler, Eddie and Krohn, Maxwell and Frey, Cliff and Ziegler, David and Kaashoek, Frans and Morris, Robert and Mazieres, David},
  journal={ACM Transactions on Computer Systems (TOCS)},
  volume={25},
  number={4},
  pages={11--es},
  year={2007},
  publisher={ACM New York, NY, USA}
}


@article{zeldovich2011making,
  title={Making information flow explicit in HiStar},
  author={Zeldovich, Nickolai and Boyd-Wickizer, Silas and Kohler, Eddie and Mazi{\`e}res, David},
  journal={Communications of the ACM},
  volume={54},
  number={11},
  pages={93--101},
  year={2011},
  publisher={ACM New York, NY, USA}
}


@article{krohn2007information,
  title={Information flow control for standard OS abstractions},
  author={Krohn, Maxwell and Yip, Alexander and Brodsky, Micah and Cliffer, Natan and Kaashoek, M Frans and Kohler, Eddie and Morris, Robert},
  journal={ACM SIGOPS Operating Systems Review},
  volume={41},
  number={6},
  pages={321--334},
  year={2007},
  publisher={ACM New York, NY, USA}
}


@inproceedings{roy2009laminar,
  title={Laminar: Practical fine-grained decentralized information flow control},
  author={Roy, Indrajit and Porter, Donald E and Bond, Michael D and McKinley, Kathryn S and Witchel, Emmett},
  booktitle={Proceedings of the 30th ACM SIGPLAN Conference on Programming Language Design and Implementation},
  pages={63--74},
  year={2009}
}


@article{porter2014practical,
  title={Practical fine-grained information flow control using laminar},
  author={Porter, Donald E and Bond, Michael D and Roy, Indrajit and McKinley, Kathryn S and Witchel, Emmett},
  journal={ACM Transactions on Programming Languages and Systems (TOPLAS)},
  volume={37},
  number={1},
  pages={1--51},
  year={2014},
  publisher={ACM New York, NY, USA}
}

@inproceedings{harada2005towards,
  title={Towards a manageable Linux security},
  author={Harada, Toshiharu and Horie, Takashi and Tanaka, Kazuo},
  booktitle={Linux Conference},
  volume={2005},
  year={2005}
}


@article{bishop1996checking,
  title={Checking for race conditions in file accesses},
  author={Bishop, Matt and Dilger, Michael and others},
  journal={Computing systems},
  volume={2},
  number={2},
  pages={131--152},
  year={1996}
}

@inproceedings{morris2008perfselinux,
  title={Have you driven an SELinux lately},
  author={Morris, James},
  booktitle={Linux Symposium Proceedings},
  year={2008}
}

@inproceedings{ltp,
  title={Building a robust linux kernel piggybacking the linux test project},
  author={Modak, Subrata and Singh, Balbir},
  booktitle={Linux Symposium (OLS)},
  year={2008},
  organization={Citeseer}
}

@InProceedings{swamy2010enforing,
  author="Swamy, Nikhil and Chen, Juan and Chugh, Ravi",
  editor="Gordon, Andrew D.",
  title="Enforcing Stateful Authorization and Information Flow Policies in Fine",
  booktitle="Programming Languages and Systems",
  year="2010",
  publisher="Springer Berlin Heidelberg",
  address="Berlin, Heidelberg",
  pages="529--549",
  abstract="Proving software free of security bugs is hard. Languages that ensure that programs correctly enforce their security policies would help, but, to date, no security-typed language has the ability to verify the enforcement of the kinds of policies used in practice---dynamic, stateful policies which address a range of concerns including forms of access control and information flow tracking.",
  isbn="978-3-642-11957-6"
}

@misc{PhoronixFS,
  Author = {Michael Larabel},
  Howpublished = {\url{https://www.kernel.org/doc/html/latest/security/lsm.html}},
  Note = {Last Accessed May. 21, 2020},
  Title = {Linux Security Modules: General Security Hooks for Linux},
}


@inproceedings{schaufler2008smack,
  title={Smack in embedded computing},
  author={Schaufler, Casey},
  booktitle={Proc. Ottawa Linux Symposium},
  pages={23},
  year={2008}
}

@TECHREPORT{Smalley02implementingselinux,
    author = {Stephen Smalley and Chris Vance and Wayne Salamon},
    title = {Implementing SELinux as a linux security module},
    institution = {},
    year = {2002}
}


@inproceedings{rao2009dynamic,
title = "Dynamic mandatory access control for multiple stakeholders",
author = "Vikhyath Rao and Trent Jaeger",
year = "2009",
month = nov,
day = "30",
doi = "10.1145/1542207.1542217",
language = "English (US)",
isbn = "9781605585376",
series = "Proceedings of ACM Symposium on Access Control Models and Technologies, SACMAT",
pages = "53--62",
booktitle = "SACMAT'09 - Proceedings of the 14th ACM Symposium on Access Control Models and Technologies",
note = "14th ACM Symposium on Access Control Models and Technologies, SACMAT 2009 ; Conference date: 03-06-2009 Through 05-06-2009",
}

@inproceedings{Smalley2013SecurityE,
  title={Security Enhanced (SE) Android: Bringing Flexible MAC to Android},
  author={Stephen Dale Smalley and Robert Craig},
  booktitle={NDSS},
  year={2013}
}

@article{Hicks2010seLinuxMLS,
author = {Hicks, Boniface and Rueda, Sandra and St.Clair, Luke and Jaeger, Trent and McDaniel, Patrick},
title = {A Logical Specification and Analysis for SELinux MLS Policy},
year = {2010},
issue_date = {July 2010},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
volume = {13},
number = {3},
issn = {1094-9224},
url = {https://doi.org/10.1145/1805974.1805982},
doi = {10.1145/1805974.1805982},
journal = {ACM Trans. Inf. Syst. Secur.},
month = jul,
articleno = {26},
numpages = {31},
keywords = {policy compliance, SELinux, policy analysis, multilevel security}
}

@inproceedings{zanin2004towards,
author = {Zanin, Giorgio and Mancini, Luigi Vincenzo},
title = {Towards a Formal Model for Security Policies Specification and Validation in the Selinux System},
year = {2004},
isbn = {1581138725},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/990036.990059},
doi = {10.1145/990036.990059},
booktitle = {Proceedings of the Ninth ACM Symposium on Access Control Models and Technologies},
pages = {136–145},
numpages = {10},
keywords = {formal model, configuration, security enhanced linux},
location = {Yorktown Heights, New York, USA},
series = {SACMAT 04}
}
  

@inproceedings{jaeger2003analyzing,
  title={Analyzing Integrity Protection in the SELinux Example Policy.},
  author={Jaeger, Trent and Sailer, Reiner and Zhang, Xiaolan},
  booktitle={USENIX Security Symposium},
  volume={12},
  pages={5},
  year={2003}
}

@INPROCEEDINGS{,
  author={J. {Ko} and S. {Lee} and C. {Lee}},
  booktitle={2019 IEEE International Conference on Consumer Electronics (ICCE)},
  title={Real-time Mandatory Access Control on SELinux for Internet of Things},
  year={2019},
  volume={},
  number={},
  pages={1-6}
 }
 
 @article{bauer2006paranoid,
  title={Paranoid penguin: an introduction to Novell AppArmor},
  author={Bauer, Mick},
  journal={Linux Journal},
  volume={2006},
  number={148},
  pages={13},
  year={2006},
  publisher={Belltown Media}
}

@inproceedings {sun2018Containers,
author = {Yuqiong Sun and David Safford and Mimi Zohar and Dimitrios Pendarakis and Zhongshu Gu and Trent Jaeger},
title = {Security Namespace: Making Linux Security Frameworks Available to Containers},
booktitle = {27th USENIX Security Symposium (USENIX Security 18)},
year = {2018},
isbn = {978-1-939133-04-5},
address = {Baltimore, MD},
pages = {1423--1439},
url = {https://www.usenix.org/conference/usenixsecurity18/presentation/sun},
publisher = {{USENIX} Association},
month = aug,
}

@article{schaufler2008simplified,
  title={The simplified mandatory access control kernel},
  author={Schaufler, Casey},
  journal={White Paper},
  pages={1--11},
  year={2008}
}



@ARTICLE{Sandhu94accesscontrol,
    author = {Ravi S. Sandhu and Pierangela Samarati},
    title = {Access Control: Principles and Practice},
    journal = {IEEE Communications Magazine},
    year = {1994},
    volume = {32},
    pages = {40--48}
}

@misc{CWE-426,
  Howpublished = {\url{https://cwe.mitre.org/data/definitions/426.html}},
  Note = {Last Accessed May. 21, 2020},
  Title = {CWE-426: Untrusted Search Path},
}

@misc{CWE-283,
  Howpublished = {\url{https://cwe.mitre.org/data/definitions/283.html}},
  Note = {Last Accessed May. 21, 2020},
  Title = {CWE-283: Unverified Ownership},
}

@misc{CWE-22,
  Howpublished = {\url{https://cwe.mitre.org/data/definitions/22.html}},
  Note = {Last Accessed May. 21, 2020},
  Title = {CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')},
}

@misc{CWE-98,
  Howpublished = {\url{https://cwe.mitre.org/data/definitions/98.html}},
  Note = {Last Accessed May. 21, 2020},
  Title = {CWE-98: Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion')},
}

@misc{CWE-59,
  Howpublished = {\url{https://cwe.mitre.org/data/definitions/59.html}},
  Note = {Last Accessed May. 21, 2020},
  Title = {CWE-59: Improper Link Resolution Before File Access ('Link Following')},
}

@misc{CWE-362,
  Howpublished = {\url{https://cwe.mitre.org/data/definitions/362.html}},
  Note = {Last Accessed May. 21, 2020},
  Title = {CWE-362: Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition')},
}

@misc{CWE-479,
  Howpublished = {\url{https://cwe.mitre.org/data/definitions/479.html}},
  Note = {Last Accessed May. 21, 2020},
  Title = {CWE-479: Signal Handler Use of a Non-reentrant Function},
}

@misc{evm,
  Howpublished = {\url{https://wiki.gentoo.org/wiki/Extended\_Verification\_Module}},
  Note = {Last Accessed May. 21, 2020},
  Title = {Extended Verification Module},
  Year={2019},
  Author={Gentoo Foundation, Inc.}
}

@misc{lsmstacking,
  Howpublished = {\url{https://lwn.net/Articles/804906/}},
  Note = {Last Accessed May. 21, 2020},
  Title = {LSM stacking and the future},
  year = {2019},
  author= {Jake Edge}
}

@misc{lsmstackingRedux,
  Howpublished = {\url{https://www.linuxplumbersconf.org/event/
 2/contributions/203/
 attachments/123/155/Namespacing\_and\_Stacking\_the\_LSM-2018.pdf}},
  Note = {Linux Plumbers Container MC 2018},
  Title = {Stacking \& LSM Namespacing Redux},
  year = {2018},
  author= {Casey Schaufler}
}



@article{jaeger2008operating,
  title={Operating system security},
  author={Jaeger, Trent},
  journal={Synthesis Lectures on Information Security, Privacy and Trust},
  volume={1},
  number={1},
  pages={1--218},
  year={2008},
  publisher={Morgan \& Claypool Publishers}
}

@inproceedings{sailer2004IMA,
author = {Sailer, Reiner and Zhang, Xiaolan and Jaeger, Trent and van Doorn, Leendert},
title = {Design and Implementation of a TCG-Based Integrity Measurement Architecture},
year = {2004},
publisher = {USENIX Association},
address = {USA},
booktitle = {Proceedings of the 13th Conference on USENIX Security Symposium - Volume 13},
pages = {16},
numpages = {1},
location = {San Diego, CA},
series = {SSYM'04}
}

@inproceedings{rajani2016access,
  title={On access control, capabilities, their equivalence, and confused deputy attacks},
  author={Rajani, Vineet and Garg, Deepak and Rezk, Tamara},
  booktitle={2016 IEEE 29th Computer Security Foundations Symposium (CSF)},
  pages={150--163},
  year={2016},
  organization={IEEE}
}

@INPROCEEDINGS{lsmInformation,
   author={L. {Georget} and M. {Jaume} and F. {Tronel} and G. {Piolle} and V. V. T. {Tong}},
   booktitle={2017 IEEE/ACM 5th International FME Workshop on Formal Methods in Software Engineering (FormaliSE)},
   title={Verifying the Reliability of Operating System-Level Information Flow Control Systems in Linux},
   year={2017},
   volume={},
   number={},
   pages={10-16}
}



%%%%%%%%%%%%%%%%%%bench


@inproceedings{mcvoy1996lmbench,
  title={lmbench: Portable tools for performance analysis.},
  author={McVoy, Larry W and Staelin, Carl and others},
  booktitle={USENIX annual technical conference},
  pages={279--294},
  year={1996},
  organization={San Diego, CA, USA}
}


@article{mcdougall2005filebench,
  title={FileBench},
  author={McDougall, Richard and Mauro, Jim},
  journal={URL: http://www. nfsv4bat. org/Documents/nasconf/2004/filebench. pdf (Cited on page 56.)},
  year={2005}
}

@misc{fsmark,
  title={FS-Mark},
  url={https://openbenchmarking.org/test/pts/fs-mark},
  journal={OpenBenchmarking.org - FS-Mark Test Profile},
  author={ OpenBenchmarking.org},
  year={2020}
}

@article{tarasov2016filebench,
  title={Filebench: A flexible framework for file system benchmarking},
  author={Tarasov, Vasily and Zadok, Erez and Shepler, Spencer},
  journal={USENIX; login},
  volume={41},
  number={1},
  pages={6--12},
  year={2016}
}

@inproceedings{DBLP:conf/usenix/McVoyS96,
  author    = {Larry W. McVoy and
               Carl Staelin},
  title     = {lmbench: Portable Tools for Performance Analysis},
  booktitle = {Proceedings of the {USENIX} Annual Technical Conference, San Diego,
               California, USA, January 22-26, 1996},
  pages     = {279--294},
  publisher = {{USENIX} Association},
  year      = {1996},
  timestamp = {Wed, 04 Jul 2018 13:06:34 +0200},
  biburl    = {https://dblp.org/rec/conf/usenix/McVoyS96.bib},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}


@article{wolman1989iobench,
  title={IOBENCH: a system independent IO benchmark},
  author={Wolman, Barry and Olson, Thomas M},
  journal={ACM SIGARCH Computer Architecture News},
  volume={17},
  number={5},
  pages={55--70},
  year={1989},
  publisher={ACM New York, NY, USA}
}



@article{smith2011byteunix,
  title={Byte-unixbench: A Unix benchmark suite},
  author={Smith, Ben and Grehan, Rick and Yager, Tom and Niemi, DC},
  journal={Technical report},
  year={2011}
}

@inproceedings{capWaton2010,
  author = {Watson, Robert N. M. and Anderson, Jonathan and Laurie, Ben and Kennaway, Kris},
  title = {Capsicum: Practical Capabilities for UNIX},
  year = {2010},
  isbn = {8887666655554},
  publisher = {USENIX Association},
  address = {USA},
  booktitle = {Proceedings of the 19th USENIX Conference on Security},
  pages = {3},
  numpages = {1},
  location = {Washington, DC},
  series = {USENIX Security’10}
}

@inproceedings{macNyanchama1996,
  author = {Nyanchama, Matunda and Osborn, Sylvia},
  title = {Modeling Mandatory Access Control in Role-Based Security Systems},
  year = {1996},
  isbn = {0412729202},
  publisher = {Chapman & Hall, Ltd.},
  address = {GBR},
  booktitle = {Proceedings of the Ninth Annual IFIP TC11 WG11.3 Working Conference on Database Security IX : Status and Prospects: Status and Prospects},
  pages = {129–144},
  numpages = {16},
  keywords = {information flow, role-based protection, security level, role, mandatory access control},
  location = {Rennselaerville, New York, USA}
}

@article{mcphee1974operating,
  title={Operating system integrity in OS/VS2},
  author={McPhee, William S.},
  journal={IBM Systems Journal},
  volume={13},
  number={3},
  pages={230--252},
  year={1974},
  publisher={IBM}
}

@article{DBLP:journals/csys/BishopD96,
  author    = {Matt Bishop and
               Michael Dilger},
  title     = {Checking for Race Conditions in File Accesses},
  journal   = {Comput. Syst.},
  volume    = {9},
  number    = {2},
  pages     = {131--152},
  year      = {1996},
  url       = {http://www.usenix.org/publications/compsystems/1996/spr\_bishop.pdf},
  timestamp = {Sun, 10 May 2020 21:17:34 +0200},
  biburl    = {https://dblp.org/rec/journals/csys/BishopD96.bib},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}


@article{marill2004advanced,
  title={Advanced statistics: linear regression, part II: multiple linear regression},
  author={Marill, Keith A},
  journal={Academic emergency medicine},
  volume={11},
  number={1},
  pages={94--102},
  year={2004},
  publisher={Wiley Online Library}
}